# 用户评论积分奖励功能测试指南

## 功能概述
用户在商品评价页面提交评论时，如果满足以下条件之一，将自动获得100积分奖励：
1. 16字以上的好评（4星及以上）
2. 上传了配图（不限字数和星级）

## 测试准备

### 1. 数据库准备
确保以下表存在并有测试数据：
- `weshop_user` - 用户表
- `weshop_points_record` - 积分记录表
- `weshop_comment` - 评论表
- `weshop_comment_picture` - 评论图片表

### 2. 测试用户准备
创建测试用户并记录初始积分：
```sql
-- 查看测试用户当前积分
SELECT id, nickname, points FROM weshop_user WHERE id = [测试用户ID];
```

## 测试用例

### 测试用例1：16字以上好评（无配图）
**测试步骤：**
1. 登录小程序，进入商品详情页
2. 点击"立即购买"完成订单
3. 在订单详情页点击"评价"按钮
4. 设置星级为4星或5星
5. 输入16字以上的评价内容（例如："这个商品质量非常好，包装精美，物流速度很快，客服态度也很棒，非常满意，推荐购买！"）
6. 不上传图片
7. 点击"提交评价"

**预期结果：**
- 评价提交成功
- 弹出提示："感谢您的评价！您已获得100积分奖励，请到个人中心查看。"
- 用户积分增加100分
- 积分记录表新增一条记录

### 测试用例2：配图评价（不限字数和星级）
**测试步骤：**
1. 登录小程序，进入商品详情页
2. 点击"立即购买"完成订单
3. 在订单详情页点击"评价"按钮
4. 设置任意星级（1-5星）
5. 输入任意长度的评价内容（可以少于16字）
6. 上传1张或多张图片
7. 点击"提交评价"

**预期结果：**
- 评价提交成功
- 弹出提示："感谢您的评价！您已获得100积分奖励，请到个人中心查看。"
- 用户积分增加100分
- 积分记录表新增一条记录

### 测试用例3：16字以上好评+配图
**测试步骤：**
1. 登录小程序，进入商品详情页
2. 点击"立即购买"完成订单
3. 在订单详情页点击"评价"按钮
4. 设置星级为4星或5星
5. 输入16字以上的评价内容
6. 上传1张或多张图片
7. 点击"提交评价"

**预期结果：**
- 评价提交成功
- 弹出提示："感谢您的评价！您已获得100积分奖励，请到个人中心查看。"
- 用户积分增加100分（只奖励一次，不重复奖励）
- 积分记录表新增一条记录

### 测试用例4：不符合奖励条件的评价
**测试步骤：**
1. 登录小程序，进入商品详情页
2. 点击"立即购买"完成订单
3. 在订单详情页点击"评价"按钮
4. 设置星级为1-3星（差评或中评）
5. 输入少于16字的评价内容（例如："一般般"）
6. 不上传图片
7. 点击"提交评价"

**预期结果：**
- 评价提交成功
- 显示普通成功提示："评价成功"
- 用户积分不变
- 积分记录表不新增记录

### 测试用例5：边界条件测试
**测试步骤：**
1. 输入恰好16字的好评内容
2. 输入15字的好评内容
3. 上传9张图片（最大限制）
4. 分别测试1-5星的评价

## 验证方法

### 1. 前端验证
- 查看提示消息是否正确显示
- 检查积分奖励提示文案是否准确

### 2. 数据库验证
```sql
-- 查看用户积分变化
SELECT id, nickname, points FROM weshop_user WHERE id = [测试用户ID];

-- 查看积分记录
SELECT * FROM weshop_points_record 
WHERE user_id = [测试用户ID] 
AND description LIKE '%评价奖励%' 
ORDER BY create_time DESC;

-- 查看评论记录
SELECT * FROM weshop_comment 
WHERE user_id = [测试用户ID] 
ORDER BY add_time DESC;

-- 查看评论图片
SELECT cp.* FROM weshop_comment_picture cp
JOIN weshop_comment c ON cp.comment_id = c.id
WHERE c.user_id = [测试用户ID]
ORDER BY c.add_time DESC;
```

### 3. 日志验证
查看服务端日志，确认积分奖励逻辑正确执行：
```
用户[用户ID]因评价获得100积分奖励
```

## 注意事项

1. **积分奖励条件**：
   - 16字以上好评：需要4星及以上 + 16字及以上内容
   - 配图评价：只要有图片即可，不限星级和字数

2. **奖励机制**：
   - 每次评价最多奖励一次100积分
   - 即使同时满足两个条件，也只奖励一次

3. **测试环境**：
   - 确保积分系统正常运行
   - 确保图片上传功能正常
   - 确保评价提交接口正常

4. **异常处理**：
   - 积分奖励失败不影响评价提交
   - 系统会记录相关日志便于排查

## 回归测试

完成功能测试后，还需要验证：
1. 原有评价功能是否正常
2. 积分系统其他功能是否受影响
3. 用户积分显示是否正确更新
4. 积分记录查询是否正常