# 商品评价图片上传功能测试指南

## 测试准备

### 1. 启动后端服务
```bash
cd server
mvn spring-boot:run
```

### 2. 准备测试图片
- 准备几张测试图片（jpg、png格式）
- 确保图片大小在5MB以内
- 建议准备不同尺寸的图片进行测试

## 测试步骤

### 步骤1：登录小程序
1. 打开微信开发者工具
2. 导入小程序项目
3. 启动小程序
4. 完成微信登录

### 步骤2：创建测试订单
1. 浏览商品列表
2. 选择一个商品
3. 点击"立即购买"
4. 填写收货地址
5. 完成支付（可以使用测试支付）

### 步骤3：进入评价页面
1. 进入"个人中心" -> "我的订单"
2. 找到刚创建的订单
3. 点击"评价"按钮
4. 进入商品评价页面

### 步骤4：测试图片上传
1. **基础功能测试**
   - 点击"添加图片"按钮
   - 选择1张图片
   - 检查图片是否正确显示在页面上
   - 点击图片预览功能

2. **多图片上传测试**
   - 继续添加图片，最多9张
   - 检查图片数量限制是否生效
   - 测试删除图片功能

3. **文件类型限制测试**
   - 尝试选择非图片文件
   - 检查是否有正确的错误提示

### 步骤5：提交评价测试
1. **纯文字评价**
   - 输入少于5个字符，检查提示
   - 输入5-15个字符，提交评价
   - 检查是否成功，是否有积分奖励

2. **文字+图片评价**
   - 输入评价内容（任意长度）
   - 添加1-3张图片
   - 提交评价
   - 检查是否获得100积分奖励

3. **长文字评价**
   - 输入16字以上的评价内容
   - 不添加图片
   - 提交评价
   - 检查是否获得100积分奖励

## 验证结果

### 1. 前端验证
- [ ] 图片选择功能正常
- [ ] 图片预览功能正常
- [ ] 图片删除功能正常
- [ ] 数量限制（最多9张）生效
- [ ] 文件类型限制生效
- [ ] 提交成功提示正确
- [ ] 积分奖励提示正确

### 2. 后端验证
检查数据库记录：

```sql
-- 查看最新的评论记录
SELECT id, type_id, value_id, content, create_time, user_id 
FROM weshop_comment 
ORDER BY create_time DESC 
LIMIT 5;

-- 查看评论图片记录
SELECT cp.id, cp.comment_id, cp.pic_url, cp.sort_order
FROM weshop_comment_picture cp
JOIN weshop_comment c ON cp.comment_id = c.id
ORDER BY c.create_time DESC
LIMIT 10;
```

### 3. 文件系统验证
检查上传的文件：
```bash
# 检查上传目录
ls -la ./uploads/

# 检查今天的上传文件
ls -la ./uploads/$(date +%Y/%m/%d)/
```

## 性能测试

### 1. 并发上传测试
- 同时上传多张图片
- 检查服务器响应时间
- 检查内存使用情况

### 2. 大文件测试
- 上传接近5MB的图片
- 检查上传时间和成功率
- 测试网络中断恢复

## 错误场景测试

### 1. 网络异常
- 上传过程中断网
- 检查错误提示和重试机制

### 2. 服务器异常
- 停止后端服务
- 检查前端错误处理

### 3. 存储空间不足
- 模拟磁盘空间不足
- 检查错误处理

## 测试检查清单

### 功能测试
- [ ] 图片选择功能
- [ ] 图片预览功能  
- [ ] 图片删除功能
- [ ] 多图片上传（最多9张）
- [ ] 文件类型限制（仅图片）
- [ ] 文件大小限制（5MB）
- [ ] 评价提交功能
- [ ] 积分奖励机制

### 数据验证
- [ ] 评论记录正确保存
- [ ] 图片记录正确保存
- [ ] 图片URL可正常访问
- [ ] 积分记录正确

### 用户体验
- [ ] 操作流程顺畅
- [ ] 错误提示友好
- [ ] 加载状态明确
- [ ] 成功反馈及时

## 常见问题排查

### 问题1：图片上传失败
**可能原因：**
- 文件过大（超过5MB）
- 文件类型不支持
- 网络连接问题
- 服务器存储空间不足

**排查步骤：**
1. 检查文件大小和类型
2. 检查网络连接
3. 查看服务器日志
4. 检查磁盘空间

### 问题2：图片不显示
**可能原因：**
- 静态资源配置错误
- 文件路径错误
- 文件权限问题

**排查步骤：**
1. 检查WebConfig配置
2. 验证文件是否存在
3. 检查文件权限
4. 测试直接访问URL

### 问题3：积分未发放
**可能原因：**
- 积分系统配置问题
- 奖励条件判断错误
- 数据库连接问题

**排查步骤：**
1. 检查积分系统配置
2. 验证奖励条件逻辑
3. 查看数据库记录
4. 检查服务日志

## 测试报告模板

```
测试时间：____年__月__日
测试人员：________
测试环境：开发环境/测试环境

功能测试结果：
□ 通过 □ 失败 - 图片选择功能
□ 通过 □ 失败 - 图片上传功能  
□ 通过 □ 失败 - 评价提交功能
□ 通过 □ 失败 - 积分奖励功能

发现问题：
1. ________________________________
2. ________________________________
3. ________________________________

建议改进：
1. ________________________________
2. ________________________________
3. ________________________________
```