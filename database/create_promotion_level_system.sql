-- 推广等级系统数据库结构
-- 创建推广等级配置表

-- 1. 创建推广等级配置表
CREATE TABLE IF NOT EXISTS `weshop_promotion_level_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `level` int(11) NOT NULL COMMENT '推广等级（1-5级）',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `min_promotion_count` int(11) NOT NULL COMMENT '最小推广人数',
  `max_promotion_count` int(11) DEFAULT NULL COMMENT '最大推广人数（NULL表示无上限）',
  `reward_points` int(11) NOT NULL COMMENT '每推广一人获得的积分',
  `level_description` varchar(200) DEFAULT NULL COMMENT '等级描述',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广等级配置表';

-- 2. 插入5级推广等级配置数据
INSERT INTO `weshop_promotion_level_config` (`level`, `level_name`, `min_promotion_count`, `max_promotion_count`, `reward_points`, `level_description`) VALUES
(1, '初级推广员', 0, 50, 100, '≤50人，每次推广1人赠送100积分'),
(2, '中级推广员', 51, 100, 200, '51到100人，每次推广1人赠送200积分'),
(3, '高级推广员', 101, 150, 300, '101到150人，每次推广1人赠送300积分'),
(4, '资深推广员', 151, 200, 300, '151到200人，每次推广1人赠送300积分'),
(5, '金牌推广员', 201, NULL, 500, '201人以上，每推广一人500积分');

-- 3. 为用户表添加推广等级相关字段（如果不存在）
ALTER TABLE `weshop_user` 
ADD COLUMN IF NOT EXISTS `promotion_level` int(11) DEFAULT 1 COMMENT '推广等级（1-5级）',
ADD COLUMN IF NOT EXISTS `promotion_count` int(11) DEFAULT 0 COMMENT '推广人数统计',
ADD COLUMN IF NOT EXISTS `promotion_time` datetime DEFAULT NULL COMMENT '成为推广者的时间',
ADD COLUMN IF NOT EXISTS `first_promotion_time` datetime DEFAULT NULL COMMENT '首次推广时间',
ADD COLUMN IF NOT EXISTS `promotion_code` varchar(20) DEFAULT NULL COMMENT '推广码',
ADD COLUMN IF NOT EXISTS `total_promotion_points` int(11) DEFAULT 0 COMMENT '推广获得的总积分';

-- 4. 创建推广积分记录表
CREATE TABLE IF NOT EXISTS `weshop_promotion_points_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `promoter_id` int(11) NOT NULL COMMENT '推广者ID',
  `promoted_user_id` int(11) NOT NULL COMMENT '被推广用户ID',
  `promotion_level` int(11) NOT NULL COMMENT '推广时的等级',
  `reward_points` int(11) NOT NULL COMMENT '获得的积分',
  `promotion_time` datetime NOT NULL COMMENT '推广时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_promoter_id` (`promoter_id`),
  KEY `idx_promoted_user_id` (`promoted_user_id`),
  KEY `idx_promotion_time` (`promotion_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广积分记录表';

-- 5. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_user_promoter_id` ON `weshop_user` (`promoter_id`);
CREATE INDEX IF NOT EXISTS `idx_user_promotion_level` ON `weshop_user` (`promotion_level`);
CREATE INDEX IF NOT EXISTS `idx_user_promotion_count` ON `weshop_user` (`promotion_count`);

-- 6. 创建触发器，自动更新推广统计（可选）
DELIMITER $$

-- 当用户的推广者ID被设置时，自动更新推广者的推广统计
CREATE TRIGGER IF NOT EXISTS `tr_update_promotion_stats` 
AFTER UPDATE ON `weshop_user`
FOR EACH ROW
BEGIN
    -- 如果推广者ID发生变化且新值不为空
    IF NEW.promoter_id IS NOT NULL AND (OLD.promoter_id IS NULL OR OLD.promoter_id != NEW.promoter_id) THEN
        -- 更新推广者的推广人数
        UPDATE `weshop_user` 
        SET `promotion_count` = (
            SELECT COUNT(*) FROM `weshop_user` u2 WHERE u2.promoter_id = NEW.promoter_id
        )
        WHERE `id` = NEW.promoter_id;
        
        -- 设置被推广用户的推广时间
        IF NEW.promotion_time IS NULL THEN
            UPDATE `weshop_user` 
            SET `promotion_time` = NOW() 
            WHERE `id` = NEW.id;
        END IF;
        
        -- 设置推广者的首次推广时间
        UPDATE `weshop_user` 
        SET `first_promotion_time` = COALESCE(`first_promotion_time`, NOW())
        WHERE `id` = NEW.promoter_id AND `first_promotion_time` IS NULL;
    END IF;
END$$

DELIMITER ;

-- 7. 初始化现有用户的推广码（基于用户ID生成）
UPDATE `weshop_user` 
SET `promotion_code` = CONCAT('P', LPAD(id, 6, '0'))
WHERE `promotion_code` IS NULL;

-- 8. 查看创建结果
SELECT '=== 推广等级配置 ===' as info;
SELECT * FROM `weshop_promotion_level_config` ORDER BY `level`;

SELECT '=== 用户表结构检查 ===' as info;
DESCRIBE `weshop_user`;

SELECT '=== 推广积分记录表结构 ===' as info;
DESCRIBE `weshop_promotion_points_record`;