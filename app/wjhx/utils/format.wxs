/**
 * 格式化图片URL
 * 如果是以http开头的完整URL，则直接返回
 * 如果是以/weshop-wjhx开头的相对路径，则补充baseUrl
 * 否则返回传入的值
 */

function formatImageUrl(url, size) {
  // 如果URL为空或undefined，返回默认图片
  if (!url || url === '') {
    return '/static/images/redeem-placeholder.png';  // 使用一个已有的占位图
  }

  // 如果已经是完整URL则直接返回
  if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
    return url;
  }

  // 如果是以/weshop-wjhx开头的路径，则加上baseUrl
  if (url.indexOf('/weshop-wjhx/uploads') === 0) {
    //var host = 'http://localhost:9999'
    var host = 'https://www.sxwjsm.com'
    var fullUrl = host + url;
    
    // 根据size参数添加图片尺寸优化
    if (size === 'thumb') {
      // 缩略图尺寸
      fullUrl += '?imageView2/2/w/200/h/200/q/80';
    } else if (size === 'medium') {
      // 中等尺寸
      fullUrl += '?imageView2/2/w/400/h/400/q/85';
    }
    
    return fullUrl;
  }

  // 其他情况直接返回原始URL
  return url;
}

/**
 * 格式化手机号码，中间4位用*代替
 * 例如：13812345678 -> 138****5678
 */
function formatMobile(mobile) {
  // 如果手机号为空或undefined，返回空字符串
  if (!mobile || mobile === '') {
    return '';
  }

  // 转换为字符串
  var mobileStr = mobile.toString();

  // 如果不是11位手机号，直接返回原值
  if (mobileStr.length !== 11) {
    return mobileStr;
  }

  // 格式化：前3位 + **** + 后4位
  return mobileStr.substring(0, 3) + '****' + mobileStr.substring(7);
}

/**
 * 格式化价格，保留两位小数
 * 例如：12.5 -> 12.50, 12 -> 12.00
 */
function formatPrice(price) {
  // 如果价格为空、undefined或null，返回0.00
  if (price === null || price === undefined || price === '') {
    return '0.00';
  }

  // 转换为数字
  var num = parseFloat(price);
  
  // 如果转换失败，返回0.00
  if (isNaN(num)) {
    return '0.00';
  }

  // 保留两位小数
  return num.toFixed(2);
}

/**
 * 格式化日期时间
 * 将ISO格式的日期转换为易读格式
 * 例如：2025-07-27T02:31:34.000+00:00 -> 2025-07-27 10:31
 */
function formatDateTime(dateTimeStr) {
  // 如果日期为空或undefined，返回空字符串
  if (!dateTimeStr || dateTimeStr === '') {
    return '';
  }

    // 创建Date对象
    var date = getDate(dateTimeStr);
    
    // 获取年月日时分
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hours = date.getHours();
    var minutes = date.getMinutes();
    
    // 补零函数
    function padZero(num) {
      return num < 10 ? '0' + num : num.toString();
    }
    
    // 格式化为 YYYY-MM-DD HH:mm
    return year + '-' + padZero(month) + '-' + padZero(day) + ' ' + padZero(hours) + ':' + padZero(minutes);
}

/**
 * 格式化相对时间
 * 显示相对于当前时间的描述
 * 例如：刚刚、5分钟前、1小时前、昨天、2天前等
 */
function formatRelativeTime(dateTimeStr) {
  // 如果日期为空或undefined，返回空字符串
  if (!dateTimeStr || dateTimeStr === '') {
    return '';
  }

    var date = getDate(dateTimeStr);
    var now = getDate();
    var diff = now.getTime() - date.getTime();
    
    // 转换为分钟
    var minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return minutes + '分钟前';
    } else if (minutes < 1440) { // 24小时内
      var hours = Math.floor(minutes / 60);
      return hours + '小时前';
    } else if (minutes < 2880) { // 48小时内
      return '昨天';
    } else if (minutes < 10080) { // 7天内
      var days = Math.floor(minutes / 1440);
      return days + '天前';
    } else {
      // 超过7天显示具体日期
      return formatDateTime(dateTimeStr);
    }
}

module.exports = {
  formatImageUrl: formatImageUrl,
  formatMobile: formatMobile,
  formatPrice: formatPrice,
  formatDateTime: formatDateTime,
  formatRelativeTime: formatRelativeTime
};
