<!-- 推广等级页面 -->
<wxs module="format" src="../../../utils/format.wxs"></wxs>

<!-- 自定义导航栏 -->
<custom-navbar title="推广等级" gradient-background="linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)" text-color="#ffffff" opacity="{{navOpacity}}" bind:back="onNavBack">
</custom-navbar>

<scroll-view class="container" style="padding-top: {{navbarHeight}}px;" scroll-y="{{true}}" bindscroll="onPageScroll" enhanced="{{true}}" show-scrollbar="{{false}}">
  <!-- 头部背景区域 -->
  <view class="header-background">
    <view class="header-content">
      <view class="header-title">推广等级制度</view>
      <view class="header-subtitle">推广越多，奖励越丰厚</view>
    </view>
  </view>

  <!-- 当前等级卡片 -->
  <view class="current-level-card" wx:if="{{!isLoading}}">
    <view class="card-header">
      <view class="header-icon">{{getLevelIcon(levelInfo.promotionLevel || 1)}}</view>
      <view class="header-title">我的当前等级</view>
    </view>
    <view class="current-level-content">
      <view class="level-name">{{levelInfo.currentLevelName || '初级推广员'}}</view>
      <view class="level-stats">
        <view class="stat-item">
          <text class="stat-label">推广人数</text>
          <text class="stat-value">{{levelInfo.promotionCount || 0}}人</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">当前奖励</text>
          <text class="stat-value">{{levelInfo.currentRewardPoints || 100}}积分/人</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">累计积分</text>
          <text class="stat-value">{{levelInfo.totalPromotionPoints || 0}}积分</text>
        </view>
      </view>
      
      <!-- 升级进度 -->
      <view class="upgrade-progress" wx:if="{{!levelInfo.isMaxLevel}}">
        <view class="progress-header">
          <text class="progress-title">升级进度</text>
          <text class="progress-target">目标：{{levelInfo.nextLevelName}}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{((levelInfo.promotionCount || 0) / ((levelInfo.promotionCount || 0) + (levelInfo.needPromotionCount || 1))) * 100}}%"></view>
        </view>
        <view class="progress-text">
          <text class="progress-current">已推广 {{levelInfo.promotionCount || 0}} 人</text>
          <text class="progress-need">还需 {{levelInfo.needPromotionCount || 0}} 人</text>
        </view>
      </view>
      
      <!-- 最高等级提示 -->
      <view class="max-level-tip" wx:if="{{levelInfo.isMaxLevel}}">
        <text class="max-level-text">🏆 恭喜！您已达到最高等级</text>
      </view>
    </view>
  </view>

  <!-- 等级制度说明 -->
  <view class="levels-section">
    <view class="section-header">
      <view class="header-icon">📊</view>
      <view class="header-title">等级制度详情</view>
    </view>
    
    <view class="levels-list">
      <view class="level-item {{levelInfo.promotionLevel === item.level ? 'current' : ''}}" wx:for="{{levelConfigs}}" wx:key="level">
        <view class="level-header">
          <view class="level-icon">{{getLevelIcon(item.level)}}</view>
          <view class="level-info">
            <view class="level-title">
              <text class="level-name">{{item.levelName}}</text>
              <text class="level-badge" wx:if="{{levelInfo.promotionLevel === item.level}}">当前</text>
            </view>
            <view class="level-range">{{getLevelRangeText(item)}}</view>
          </view>
          <view class="level-reward">
            <text class="reward-points">{{item.rewardPoints}}</text>
            <text class="reward-unit">积分/人</text>
          </view>
        </view>
        <view class="level-description">{{item.levelDescription}}</view>
      </view>
    </view>
  </view>

  <!-- 积分说明 -->
  <view class="points-section">
    <view class="section-header">
      <view class="header-icon">💰</view>
      <view class="header-title">积分价值说明</view>
    </view>
    <view class="points-content">
      <view class="points-rule">
        <text class="rule-text">💎 100积分 = 1元现金价值</text>
      </view>
      <view class="points-rule">
        <text class="rule-text">🛒 积分可用于购物抵扣</text>
      </view>
      <view class="points-rule">
        <text class="rule-text">🎁 积分可兑换精美礼品</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="action-btn secondary" bindtap="viewPointsRecords">
      <text class="btn-icon">📋</text>
      <text class="btn-text">积分记录</text>
    </view>
    <view class="action-btn primary" bindtap="goToPromotion">
      <text class="btn-icon">🚀</text>
      <text class="btn-text">立即推广</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
</scroll-view>

<wxs module="this">
  var getLevelIcon = function(level) {
    var icons = ['', '🥉', '🥈', '🥇', '💎', '👑'];
    return icons[level] || '🎯';
  };
  
  var getLevelRangeText = function(config) {
    if (config.maxPromotionCount === null) {
      return config.minPromotionCount + '人以上';
    } else if (config.minPromotionCount === 0) {
      return '≤' + config.maxPromotionCount + '人';
    } else {
      return config.minPromotionCount + '-' + config.maxPromotionCount + '人';
    }
  };
  
  module.exports = {
    getLevelIcon: getLevelIcon,
    getLevelRangeText: getLevelRangeText
  };
</wxs>