const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    levelInfo: {},
    levelConfigs: [
      {
        level: 1,
        levelName: '初级推广员',
        minPromotionCount: 0,
        maxPromotionCount: 50,
        rewardPoints: 100,
        levelDescription: '≤50人，每次推广1人赠送100积分'
      },
      {
        level: 2,
        levelName: '中级推广员',
        minPromotionCount: 51,
        maxPromotionCount: 100,
        rewardPoints: 200,
        levelDescription: '51到100人，每次推广1人赠送200积分'
      },
      {
        level: 3,
        levelName: '高级推广员',
        minPromotionCount: 101,
        maxPromotionCount: 150,
        rewardPoints: 300,
        levelDescription: '101到150人，每次推广1人赠送300积分'
      },
      {
        level: 4,
        levelName: '资深推广员',
        minPromotionCount: 151,
        maxPromotionCount: 200,
        rewardPoints: 300,
        levelDescription: '151到200人，每次推广1人赠送300积分'
      },
      {
        level: 5,
        levelName: '金牌推广员',
        minPromotionCount: 201,
        maxPromotionCount: null,
        rewardPoints: 500,
        levelDescription: '201人以上，每推广一人500积分'
      }
    ],
    isLoading: true,

    // 自定义导航栏相关
    navOpacity: 0.95,
    navbarHeight: 0,
  },

  onLoad: function (options) {
    this.initNavbar();
    this.loadPromotionLevelInfo();
  },

  /**
   * 加载推广等级信息
   */
  loadPromotionLevelInfo: function () {
    const that = this;
    that.setData({
      isLoading: true
    });

    util.request(api.GetPromotionLevelInfo).then(res => {
      console.log('推广等级信息:', res);
      
      if (res.success && res.data) {
        that.setData({
          levelInfo: res.data,
          isLoading: false
        });
      } else {
        that.setData({
          isLoading: false
        });
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('加载推广等级信息失败:', err);
      that.setData({
        isLoading: false
      });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 获取等级范围文本
   */
  getLevelRangeText: function(config) {
    if (config.maxPromotionCount === null) {
      return `${config.minPromotionCount}人以上`;
    } else if (config.minPromotionCount === 0) {
      return `≤${config.maxPromotionCount}人`;
    } else {
      return `${config.minPromotionCount}-${config.maxPromotionCount}人`;
    }
  },

  /**
   * 获取等级图标
   */
  getLevelIcon: function(level) {
    const icons = ['', '🥉', '🥈', '🥇', '💎', '👑'];
    return icons[level] || '🎯';
  },

  /**
   * 去推广
   */
  goToPromotion: function() {
    wx.navigateBack();
  },

  /**
   * 查看推广积分记录
   */
  viewPointsRecords: function() {
    wx.navigateTo({
      url: '/pages/ucenter/promotion-points-records/promotion-points-records'
    });
  },

  /**
   * 去推广
   */
  goToPromotion: function() {
    wx.navigateBack();
  },

  /**
   * 初始化导航栏
   */
  initNavbar: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44;
    const navbarHeight = statusBarHeight + titleBarHeight;

    this.setData({
      navbarHeight: navbarHeight
    });
  },

  /**
   * 导航栏返回事件
   */
  onNavBack: function() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/ucenter/me/me'
      });
    }
  },

  /**
   * 页面滚动事件
   */
  onPageScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));

    if (Math.abs(opacity - this.data.navOpacity) > 0.02) {
      this.setData({
        navOpacity: opacity
      });
    }
  }
});