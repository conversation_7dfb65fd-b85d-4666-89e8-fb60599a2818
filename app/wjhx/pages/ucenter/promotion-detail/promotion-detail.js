const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    promotionStats: {},
    filteredUsers: [],
    allUsers: [],
    filterType: 'all',
    promoterFilter: 'all', // 推广者筛选：all-全部用户, hasPromoter-有推广者, noPromoter-无推广者
    sortType: 'time', // 排序类型：time-推广时间, orders-订单数, income-月收入, invites-月邀请
    sortOrder: 'desc', // 排序顺序：desc-降序, asc-升序
    todayCount: 0,
    isLoading: true,
    showQrModal: false,
    qrCodeUrl: '',
    hasLoaded: false, // 标记是否已经加载过数据

    // 自定义导航栏相关
    navOpacity: 0.95,          // 导航栏透明度，初始稍微透明
    navbarHeight: 0,           // 导航栏高度
  },

  onLoad: function (options) {
    this.initNavbar();
    this.loadPromotionStats();
  },

  onShow: function () {
    // 只有在页面不是首次显示时才刷新数据
    // 避免与onLoad重复请求
    if (this.data.hasLoaded) {
      this.loadPromotionStats();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    // 下拉刷新时重置hasLoaded标志，强制刷新数据
    this.setData({
      hasLoaded: false
    });
    this.loadPromotionStats();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载推广统计数据
   */
  loadPromotionStats: function () {
    const that = this;
    that.setData({
      isLoading: true
    });

    // 同时加载推广统计和推广等级信息
    Promise.all([
      util.request(api.GetPromotionStats),
      util.request(api.GetPromotionLevelInfo)
    ]).then(([statsRes, levelRes]) => {
      console.log('推广统计数据:', statsRes);
      console.log('推广等级数据:', levelRes);
      
      if (statsRes.success && statsRes.data) {
        const stats = statsRes.data;
        const users = stats.promotedUsers || [];
        
        // 添加推广等级信息
        if (levelRes.success && levelRes.data) {
          stats.levelInfo = levelRes.data;
        }
        
        // 格式化时间并计算今日新增
        const formattedUsers = users.map(user => {
          const promotionTime = new Date(user.promotionTime);
          const registerTime = new Date(user.registerTime);
          const now = new Date();
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

          return {
            ...user,
            promotionTimeFormatted: that.formatTime(promotionTime),
            registerTimeFormatted: that.formatTime(registerTime),
            isNew: promotionTime >= today,
            // 添加统计信息字段（从后端返回的数据中获取，如果没有则设置默认值）
            totalOrderCount: user.totalOrderCount || 0,
            todayEstimatedIncome: user.todayEstimatedIncome || '0.00',
            monthEstimatedIncome: user.monthEstimatedIncome || '0.00',
            todayInviteCount: user.todayInviteCount || 0,
            monthInviteCount: user.monthInviteCount || 0,
            totalOrderAmount: user.totalOrderAmount || '0.00'
          };
        });

        // 计算今日新增数量
        const todayCount = formattedUsers.filter(user => user.isNew).length;

        that.setData({
          promotionStats: stats,
          allUsers: formattedUsers,
          todayCount: todayCount,
          isLoading: false,
          hasLoaded: true // 标记数据已加载
        });
        
        // 应用筛选条件
        that.applyFilters();
      } else {
        that.setData({
          isLoading: false,
          hasLoaded: true // 即使失败也标记为已加载，避免重复请求
        });
        wx.showToast({
          title: statsRes.msg || '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('加载推广统计失败:', err);
      that.setData({
        isLoading: false,
        hasLoaded: true // 网络错误时也标记为已加载，避免重复请求
      });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 设置筛选条件
   */
  setFilter: function (e) {
    const filterType = e.currentTarget.dataset.type;
    this.setData({
      filterType: filterType
    });
    this.applyFilters();
  },

  /**
   * 设置推广者筛选条件
   */
  setPromoterFilter: function (e) {
    const promoterFilter = e.currentTarget.dataset.type;
    this.setData({
      promoterFilter: promoterFilter
    });
    this.applyFilters();
  },

  /**
   * 应用所有筛选条件
   */
  applyFilters: function () {
    let filteredUsers = this.data.allUsers;

    // 1. 先应用时间筛选
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    switch (this.data.filterType) {
      case 'today':
        filteredUsers = filteredUsers.filter(user => {
          const promotionTime = new Date(user.promotionTime);
          return promotionTime >= today;
        });
        break;
      case 'week':
        filteredUsers = filteredUsers.filter(user => {
          const promotionTime = new Date(user.promotionTime);
          return promotionTime >= weekStart;
        });
        break;
      case 'month':
        filteredUsers = filteredUsers.filter(user => {
          const promotionTime = new Date(user.promotionTime);
          return promotionTime >= monthStart;
        });
        break;
      // 'all' 不需要时间筛选
    }

    // 2. 再应用推广者筛选
    switch (this.data.promoterFilter) {
      case 'hasPromoter':
        filteredUsers = filteredUsers.filter(user => user.promoterId);
        break;
      case 'noPromoter':
        filteredUsers = filteredUsers.filter(user => !user.promoterId);
        break;
      // 'all' 不需要推广者筛选
    }

    // 3. 排序并更新显示
    const sortedUsers = this.sortUsers(filteredUsers, this.data.sortType, this.data.sortOrder);
    this.setData({
      filteredUsers: sortedUsers
    });
  },

  /**
   * 设置排序类型
   */
  setSortType: function (e) {
    const newSortType = e.currentTarget.dataset.type;
    const currentSortType = this.data.sortType;
    const currentSortOrder = this.data.sortOrder;
    
    let newSortOrder = 'desc'; // 默认降序
    
    // 如果点击的是当前排序类型，则切换排序顺序
    if (newSortType === currentSortType) {
      newSortOrder = currentSortOrder === 'desc' ? 'asc' : 'desc';
    }
    
    this.setData({
      sortType: newSortType,
      sortOrder: newSortOrder
    });
    
    // 重新应用筛选和排序
    this.applyFilters();
  },

  /**
   * 排序用户列表
   */
  sortUsers: function (users, sortType, sortOrder) {
    if (!users || users.length === 0) return users;
    
    const sortedUsers = [...users].sort((a, b) => {
      let valueA, valueB;
      
      switch (sortType) {
        case 'time':
          // 按推广时间排序
          valueA = new Date(a.promotionTime).getTime();
          valueB = new Date(b.promotionTime).getTime();
          break;
        case 'orders':
          // 按订单数排序
          valueA = parseFloat(a.totalOrderCount) || 0;
          valueB = parseFloat(b.totalOrderCount) || 0;
          break;
        case 'income':
          // 按月收入排序
          valueA = parseFloat(a.monthEstimatedIncome) || 0;
          valueB = parseFloat(b.monthEstimatedIncome) || 0;
          break;
        case 'todayIncome':
          // 按今日收入排序
          valueA = parseFloat(a.todayEstimatedIncome) || 0;
          valueB = parseFloat(b.todayEstimatedIncome) || 0;
          break;
        case 'invites':
          // 按月邀请数排序
          valueA = parseFloat(a.monthInviteCount) || 0;
          valueB = parseFloat(b.monthInviteCount) || 0;
          break;
        case 'todayInvites':
          // 按今日邀请数排序
          valueA = parseFloat(a.todayInviteCount) || 0;
          valueB = parseFloat(b.todayInviteCount) || 0;
          break;
        case 'amount':
          // 按订单金额排序
          valueA = parseFloat(a.totalOrderAmount) || 0;
          valueB = parseFloat(b.totalOrderAmount) || 0;
          break;
        default:
          return 0;
      }
      
      // 处理相等的情况，使用推广时间作为次要排序条件
      if (valueA === valueB && sortType !== 'time') {
        const timeA = new Date(a.promotionTime).getTime();
        const timeB = new Date(b.promotionTime).getTime();
        return timeB - timeA; // 时间总是降序
      }
      
      if (sortOrder === 'desc') {
        return valueB - valueA;
      } else {
        return valueA - valueB;
      }
    });
    
    return sortedUsers;
  },

  /**
   * 格式化时间
   */
  formatTime: function (date) {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    const hours = Math.floor(diff / (60 * 60 * 1000));
    const minutes = Math.floor(diff / (60 * 1000));

    if (days > 0) {
      if (days === 1) return '昨天';
      if (days < 7) return `${days}天前`;
      return `${date.getMonth() + 1}-${date.getDate()}`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  /**
   * 生成推广二维码
   */
  generateQrCode: function () {
    const that = this;
    
    that.setData({
      showQrModal: true,
      qrCodeUrl: ''
    });

    util.request(api.GeneratePromotionQrCode).then(res => {
      if (res.success && res.data) {
        that.setData({
          qrCodeUrl: res.data.qrCodeUrl
        });
      } else {
        wx.showToast({
          title: res.msg || '生成失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('生成二维码失败:', err);
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 关闭二维码弹窗
   */
  closeQrModal: function () {
    this.setData({
      showQrModal: false,
      qrCodeUrl: ''
    });
  },

  /**
   * 保存二维码到相册
   */
  saveQrCode: function () {
    const that = this;
    const qrCodeUrl = that.data.qrCodeUrl;
    
    if (!qrCodeUrl) {
      wx.showToast({
        title: '二维码还未生成完成',
        icon: 'none'
      });
      return;
    }

    // 获取保存图片权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              that.doSaveQrCode(qrCodeUrl);
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false
              });
            }
          });
        } else {
          that.doSaveQrCode(qrCodeUrl);
        }
      }
    });
  },

  /**
   * 执行保存二维码
   */
  doSaveQrCode: function (qrCodeUrl) {
    if (qrCodeUrl.startsWith('data:image')) {
      // 处理base64格式
      const base64Data = qrCodeUrl.split(',')[1];
      const filePath = wx.env.USER_DATA_PATH + '/promotion_qr_' + Date.now() + '.png';
      
      wx.getFileSystemManager().writeFile({
        filePath: filePath,
        data: base64Data,
        encoding: 'base64',
        success: () => {
          wx.saveImageToPhotosAlbum({
            filePath: filePath,
            success: () => {
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail: (err) => {
              console.error('保存失败:', err);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        },
        fail: (err) => {
          console.error('写入文件失败:', err);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 分享二维码
   */
  shareQrCode: function () {
    this.closeQrModal();
    wx.showModal({
      title: '分享推广二维码',
      content: '点击右上角菜单选择"转发"即可分享给好友',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 分享推广
   */
  sharePromotion: function () {
    wx.showModal({
      title: '分享推广',
      content: '点击右上角菜单选择"转发"即可分享推广链接',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 去推广
   */
  goToPromotion: function () {
    wx.navigateBack();
  },

  /**
   * 获取排序类型名称
   */
  getSortTypeName: function (sortType) {
    const sortTypeNames = {
      'time': '推广时间',
      'orders': '订单数',
      'income': '月收入',
      'todayIncome': '今日收入',
      'invites': '月邀请',
      'todayInvites': '今日邀请',
      'amount': '订单金额'
    };
    return sortTypeNames[sortType] || '推广时间';
  },

  /**
   * 获取筛选状态描述文本
   */
  getFilterStatusText: function (filterType, promoterFilter) {
    let timeText = '';
    let promoterText = '';

    // 时间筛选文本
    switch (filterType) {
      case 'today':
        timeText = '今日';
        break;
      case 'week':
        timeText = '本周';
        break;
      case 'month':
        timeText = '本月';
        break;
      default:
        timeText = '';
    }

    // 推广者筛选文本
    switch (promoterFilter) {
      case 'hasPromoter':
        promoterText = '有推广者';
        break;
      case 'noPromoter':
        promoterText = '无推广者';
        break;
      default:
        promoterText = '全部用户';
    }

    // 组合文本
    if (timeText && promoterText !== '全部用户') {
      return `显示${timeText}${promoterText}`;
    } else if (timeText) {
      return `显示${timeText}用户`;
    } else if (promoterText !== '全部用户') {
      return `显示${promoterText}`;
    } else {
      return '显示全部用户';
    }
  },



  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    const userInfo = wx.getStorageSync('userInfo');
    const promoterId = userInfo ? userInfo.id : '';

    return {
      title: `${userInfo.nickname || '好友'}邀请您体验伍俊惠选`,
      desc: '精选优质商品，品质生活选择，新用户专享优惠',
      path: `/pages/index/index?promo=${promoterId}`,
      imageUrl: userInfo.avatar || ''
    };
  },

  /**
   * 初始化导航栏
   */
  initNavbar: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44;
    const navbarHeight = statusBarHeight + titleBarHeight;

    this.setData({
      navbarHeight: navbarHeight
    });
  },

  /**
   * 导航栏返回事件
   */
  onNavBack: function() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/ucenter/me/me'
      });
    }
  },

  /**
   * 页面滚动事件 - 动态调整导航栏透明度
   */
  onPageScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    // 根据滚动距离计算透明度
    // 滚动0-100rpx时，透明度从0.95变化到1
    let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));

    // 避免频繁更新，只在透明度变化超过0.02时才更新
    if (Math.abs(opacity - this.data.navOpacity) > 0.02) {
      this.setData({
        navOpacity: opacity
      });
    }
  },

  /**
   * 显示分享弹窗
   */
  showShareModal: function() {
    this.sharePromotion();
  },

  /**
   * 查看用户详情
   */
  viewUserDetail: function(e) {
    const userId = e.currentTarget.dataset.userId;
    const user = e.currentTarget.dataset.user;
    
    if (userId) {
      wx.navigateTo({
        url: `/pages/ucenter/promotion-user-detail/promotion-user-detail?userId=${userId}&nickname=${encodeURIComponent(user.nickname || '匿名用户')}`
      });
    }
  }
});
