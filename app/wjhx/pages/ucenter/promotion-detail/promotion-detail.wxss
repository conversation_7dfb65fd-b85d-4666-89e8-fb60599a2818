/* 推广明细页面样式 */
.container {
  height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

/* 头部背景区域 - 与导航栏融合 */
.header-background {
  background: linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%);
  padding: 20rpx 30rpx 60rpx;
  margin-top: -20rpx; /* 向上偏移，与导航栏无缝连接 */
  position: relative;
  overflow: hidden;
}

.header-background::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.header-background::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 150rpx;
  height: 150rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

.header-content {
  text-align: center;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  font-weight: 400;
}

/* 统计卡片 */
.stats-card {
  margin: -40rpx 20rpx 20rpx; /* 向上偏移，与头部区域重叠 */
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid rgba(66, 165, 245, 0.1);
  position: relative;
  z-index: 2;
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  background: linear-gradient(45deg, #42A5F5, #bee1f8);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, transparent, #ddd, transparent);
}

.promotion-code {
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(190, 225, 248, 0.1) 100%);
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  border: 2rpx solid rgba(66, 165, 245, 0.2);
}

.code-label {
  font-size: 24rpx;
  color: #666;
}

.code-value {
  font-size: 28rpx;
  font-weight: 700;
  color: #42A5F5;
  margin-left: 8rpx;
}

/* 推广等级信息 */
.level-info {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(255, 193, 7, 0.2);
}

.level-current {
  text-align: center;
  margin-bottom: 16rpx;
}

.level-name {
  font-size: 30rpx;
  font-weight: 700;
  color: #f57c00;
  display: block;
  margin-bottom: 8rpx;
}

.level-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.level-reward {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.reward-label {
  font-size: 24rpx;
  color: #666;
}

.reward-points {
  font-size: 26rpx;
  font-weight: 700;
  color: #f57c00;
  margin-left: 8rpx;
}

.level-progress {
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-label {
  font-size: 24rpx;
  color: #666;
}

.progress-count {
  font-size: 26rpx;
  font-weight: 700;
  color: #ff5722;
  margin-left: 8rpx;
}

.level-max {
  text-align: center;
}

.max-label {
  font-size: 26rpx;
  font-weight: 700;
  color: #4caf50;
}

/* 推广者卡片 */
.promoter-card {
  margin: 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(66, 165, 245, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.header-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.promoter-info {
  display: flex;
  align-items: center;
}

.promoter-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid #42A5F5;
}

.promoter-details {
  flex: 1;
}

.promoter-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.promoter-desc {
  font-size: 24rpx;
  color: #666;
}

/* 用户列表区域 */
.user-list-section {
  margin: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.header-count {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 筛选器区域 */
.filter-section {
  margin-bottom: 24rpx;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding: 0 10rpx;
}

/* 筛选器 */
.filter-bar {
  display: flex;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #666;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

/* 推广者筛选器特殊样式 */
.promoter-filter .filter-item {
  font-size: 24rpx;
  padding: 14rpx 8rpx;
}

.promoter-filter .filter-item.active {
  background: linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%);
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.3);
}

/* 排序器 */
.sort-bar {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 筛选和排序状态提示 */
.filter-status {
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(190, 225, 248, 0.1) 100%);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(66, 165, 245, 0.2);
  padding: 16rpx 20rpx;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.status-text {
  font-size: 26rpx;
  color: #42A5F5;
  font-weight: 600;
}

.status-count {
  font-size: 22rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.sort-info {
  text-align: center;
}

.sort-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.sort-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  margin-right: 20rpx;
  white-space: nowrap;
}

.sort-options {
  display: flex;
  flex: 1;
  white-space: nowrap;
}

.sort-item {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  margin-right: 12rpx;
  flex-shrink: 0;
  justify-content: center;
}

.sort-item.active {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  border-color: #42A5F5;
  box-shadow: 0 2rpx 8rpx rgba(66, 165, 245, 0.3);
}

.sort-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sort-item.active .sort-text {
  color: #ffffff;
  font-weight: 600;
}

.sort-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 6rpx;
  transition: all 0.3s ease;
  transform: rotate(0deg);
}

.sort-item.active .sort-arrow {
  color: #ffffff;
}

.sort-arrow.desc {
  transform: rotate(180deg);
}

.sort-arrow.asc {
  transform: rotate(0deg);
}

/* 用户列表 */
.user-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.user-item:last-child {
  border-bottom: none;
}

.user-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.user-item::after {
  content: '>';
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  font-size: 32rpx;
  font-weight: bold;
}

.user-avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #e9ecef;
}

.user-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-time, .user-register-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.time-label {
  color: #999;
}

.time-value {
  color: #666;
  font-weight: 500;
}

/* 推广者信息样式 */
.promoter-info-row {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.promoter-label {
  color: #999;
  margin-right: 8rpx;
}

.promoter-value {
  margin-right: 8rpx;
  font-weight: 500;
}

.promoter-value.has-promoter {
  color: #42A5F5;
}

.promoter-value.no-promoter {
  color: #666;
}

.promoter-badge {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
  line-height: 1.2;
}

.promoter-badge.has-promoter {
  background: linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%);
  color: #ffffff;
}

.promoter-badge.no-promoter {
  background: linear-gradient(135deg, #95a5a6 0%, #bdc3c7 100%);
  color: #ffffff;
}



/* 用户统计信息样式 */
.user-stats {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border: 1rpx solid #dee2e6;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stat-item-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 8rpx 4rpx;
}

.stat-label-small {
  font-size: 20rpx;
  color: #6c757d;
  margin-bottom: 4rpx;
  font-weight: 500;
}

.stat-value-small {
  font-size: 24rpx;
  font-weight: 600;
  color: #495057;
}

.stat-value-small.income {
  color: #28a745;
}

.stat-value-small.amount {
  color: #fd7e14;
}

.user-status {
  margin-left: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.status-badge.new {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
}

.status-badge.active {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 18rpx rgba(255, 107, 53, 0.3);
}

.action-icon {
  margin-right: 8rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 18rpx rgba(66, 165, 245, 0.3);
}

.action-btn.primary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.4);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 18rpx rgba(66, 165, 245, 0.3);
}

.action-btn.secondary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.4);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 二维码弹窗 */
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  position: relative;
  z-index: 1000;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-container {
  text-align: center;
  margin-bottom: 30rpx;
}

.qr-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.qr-loading {
  width: 400rpx;
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  color: #666;
  font-size: 28rpx;
}

.qr-actions {
  display: flex;
  gap: 20rpx;
}

.qr-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
}

.qr-btn.save {
  background: linear-gradient(135deg, #42A5F5 0%, #1976D2 100%);
}

.qr-btn.share {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
}
