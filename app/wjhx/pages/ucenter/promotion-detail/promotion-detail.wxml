<!-- 推广明细页面 -->
<wxs module="format" src="../../../utils/format.wxs"></wxs>
<!-- 自定义导航栏 -->
<custom-navbar title="推广明细" gradient-background="linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%)" text-color="#ffffff" opacity="{{navOpacity}}" bind:back="onNavBack">
  <view slot="right" bindtap="showShareModal">
    <text style="color: #ffffff; font-size: 28rpx;">分享</text>
  </view>
</custom-navbar>
<scroll-view class="container" style="padding-top: {{navbarHeight}}px;" scroll-y="{{true}}" bindscroll="onPageScroll" enhanced="{{true}}" show-scrollbar="{{false}}">
  <!-- 头部背景区域 -->
  <view class="header-background">
    <view class="header-content">
      <view class="header-title">推广中心</view>
      <view class="header-subtitle">分享赚取佣金，邀请好友共享优惠</view>
    </view>
  </view>
  <!-- 头部统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <view class="stats-icon">🎯</view>
      <view class="stats-title">我的推广统计</view>
    </view>
    <view class="stats-content">
      <view class="stat-item">
        <view class="stat-number">{{promotionStats.promotionCount || 0}}</view>
        <view class="stat-label">推广用户</view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <view class="stat-number">{{promotionStats.promotionLevel || 1}}</view>
        <view class="stat-label">推广等级</view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <view class="stat-number">{{todayCount || 0}}</view>
        <view class="stat-label">今日新增</view>
      </view>
    </view>
    
    <!-- 推广等级信息 -->
    <view class="level-info" wx:if="{{promotionStats.levelInfo}}">
      <view class="level-current">
        <text class="level-name">{{promotionStats.levelInfo.currentLevelName || '初级推广员'}}</text>
        <text class="level-desc">{{promotionStats.levelInfo.currentLevelDescription}}</text>
      </view>
      <view class="level-reward">
        <text class="reward-label">当前奖励：</text>
        <text class="reward-points">{{promotionStats.levelInfo.currentRewardPoints || 100}}积分/人</text>
      </view>
      <view class="level-progress" wx:if="{{!promotionStats.levelInfo.isMaxLevel}}">
        <text class="progress-label">距离下一等级还需：</text>
        <text class="progress-count">{{promotionStats.levelInfo.needPromotionCount || 0}}人</text>
      </view>
      <view class="level-max" wx:if="{{promotionStats.levelInfo.isMaxLevel}}">
        <text class="max-label">🏆 已达到最高等级</text>
      </view>
    </view>
    
    <view class="promotion-code" wx:if="{{promotionStats.promotionCode}}">
      <text class="code-label">我的推广码：</text>
      <text class="code-value">{{promotionStats.promotionCode}}</text>
    </view>
  </view>
  <!-- 推广者信息 -->
  <view class="promoter-card" wx:if="{{promotionStats.promoter}}">
    <view class="card-header">
      <view class="header-icon">👑</view>
      <view class="header-title">我的推广者</view>
    </view>
    <view class="promoter-info">
      <image class="promoter-avatar" src="{{promotionStats.promoter.avatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
      <view class="promoter-details">
        <view class="promoter-name">{{promotionStats.promoter.nickname || '匿名用户'}}</view>
        <view class="promoter-desc">感谢TA的推广邀请</view>
      </view>
    </view>
  </view>
  <!-- 推广用户列表 -->
  <view class="user-list-section">
    <view class="section-header">
      <view class="header-icon">👥</view>
      <view class="header-title">推广用户明细</view>
      <view class="header-count">({{promotionStats.promotionCount || 0}}人)</view>
    </view>
    <!-- 时间筛选器 -->
    <view class="filter-section">
      <view class="filter-label">时间筛选：</view>
      <view class="filter-bar">
        <view class="filter-item {{filterType === 'all' ? 'active' : ''}}" bindtap="setFilter" data-type="all">
          全部
        </view>
        <view class="filter-item {{filterType === 'today' ? 'active' : ''}}" bindtap="setFilter" data-type="today">
          今日
        </view>
        <view class="filter-item {{filterType === 'week' ? 'active' : ''}}" bindtap="setFilter" data-type="week">
          本周
        </view>
        <view class="filter-item {{filterType === 'month' ? 'active' : ''}}" bindtap="setFilter" data-type="month">
          本月
        </view>
      </view>
    </view>

    <!-- 推广者筛选器 -->
    <view class="filter-section">
      <view class="filter-label">推广者筛选：</view>
      <view class="filter-bar promoter-filter">
        <view class="filter-item {{promoterFilter === 'all' ? 'active' : ''}}" bindtap="setPromoterFilter" data-type="all">
          全部用户
        </view>
        <view class="filter-item {{promoterFilter === 'hasPromoter' ? 'active' : ''}}" bindtap="setPromoterFilter" data-type="hasPromoter">
          有推广者
        </view>
        <view class="filter-item {{promoterFilter === 'noPromoter' ? 'active' : ''}}" bindtap="setPromoterFilter" data-type="noPromoter">
          无推广者
        </view>
      </view>
    </view>

    <!-- 排序器 -->
    <view class="sort-bar">
      <view class="sort-label">排序：</view>
      <scroll-view class="sort-options" scroll-x="true" show-scrollbar="false">
        <view class="sort-item {{sortType === 'time' ? 'active' : ''}}" bindtap="setSortType" data-type="time">
          <text class="sort-text">推广时间</text>
          <text class="sort-arrow {{sortType === 'time' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
        <view class="sort-item {{sortType === 'orders' ? 'active' : ''}}" bindtap="setSortType" data-type="orders">
          <text class="sort-text">订单数</text>
          <text class="sort-arrow {{sortType === 'orders' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
        <view class="sort-item {{sortType === 'income' ? 'active' : ''}}" bindtap="setSortType" data-type="income">
          <text class="sort-text">月收入</text>
          <text class="sort-arrow {{sortType === 'income' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
        <view class="sort-item {{sortType === 'todayIncome' ? 'active' : ''}}" bindtap="setSortType" data-type="todayIncome">
          <text class="sort-text">今日收入</text>
          <text class="sort-arrow {{sortType === 'todayIncome' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
        <view class="sort-item {{sortType === 'invites' ? 'active' : ''}}" bindtap="setSortType" data-type="invites">
          <text class="sort-text">月邀请</text>
          <text class="sort-arrow {{sortType === 'invites' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
        <view class="sort-item {{sortType === 'todayInvites' ? 'active' : ''}}" bindtap="setSortType" data-type="todayInvites">
          <text class="sort-text">今日邀请</text>
          <text class="sort-arrow {{sortType === 'todayInvites' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
        <view class="sort-item {{sortType === 'amount' ? 'active' : ''}}" bindtap="setSortType" data-type="amount">
          <text class="sort-text">订单金额</text>
          <text class="sort-arrow {{sortType === 'amount' ? (sortOrder === 'desc' ? 'desc' : 'asc') : ''}}">
            ↕
          </text>
        </view>
      </scroll-view>
    </view>
    <!-- 筛选和排序状态提示 -->
    <view class="filter-status" wx:if="{{filteredUsers.length > 0}}">
      <view class="status-row">
        <text class="status-text">{{getFilterStatusText(filterType, promoterFilter)}}</text>
        <text class="status-count">共{{filteredUsers.length}}人</text>
      </view>
      <view class="sort-info">
        <text class="sort-text">按{{getSortTypeName(sortType)}}{{sortOrder === 'desc' ? '降序' : '升序'}}排列</text>
      </view>
    </view>
    <!-- 用户列表 -->
    <view class="user-list" wx:if="{{filteredUsers.length > 0}}">
      <view class="user-item" wx:for="{{filteredUsers}}" wx:key="id" bindtap="viewUserDetail" data-user-id="{{item.id}}" data-user="{{item}}">
        <view class="user-avatar-container">
          <image class="user-avatar" src="{{item.avatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
          <view class="user-badge">{{index + 1}}</view>
        </view>
        <view class="user-info">
          <view class="user-name">{{item.nickname || '匿名用户'}}</view>
          <view class="user-time">
            <text class="time-label">推广时间：</text>
            <text class="time-value">{{item.promotionTimeFormatted}}</text>
          </view>
          <!-- 推广者信息 -->
          <view class="promoter-info-row">
            <text class="promoter-label">推广者：</text>
            <text class="promoter-value {{item.promoterId ? 'has-promoter' : 'no-promoter'}}">
              {{item.promoterId ? (item.promoterNickname || '有推广者') : '直接注册'}}
            </text>
            <view class="promoter-badge {{item.promoterId ? 'has-promoter' : 'no-promoter'}}" wx:if="{{item.promoterId}}">👑</view>
            <view class="promoter-badge no-promoter" wx:else>🆓</view>
          </view>
          <!-- 统计信息区域 -->
          <view class="user-stats">
            <view class="stats-row">
              <view class="stat-item-small">
                <text class="stat-label-small">订单数</text>
                <text class="stat-value-small">{{item.totalOrderCount || 0}}</text>
              </view>
              <view class="stat-item-small">
                <text class="stat-label-small">今日收入</text>
                <text class="stat-value-small income">
                  ¥{{item.todayEstimatedIncome || '0.00'}}
                </text>
              </view>
              <view class="stat-item-small">
                <text class="stat-label-small">本月收入</text>
                <text class="stat-value-small income">
                  ¥{{item.monthEstimatedIncome || '0.00'}}
                </text>
              </view>
            </view>
            <view class="stats-row">
              <view class="stat-item-small">
                <text class="stat-label-small">今日邀请</text>
                <text class="stat-value-small">{{item.todayInviteCount || 0}}人</text>
              </view>
              <view class="stat-item-small">
                <text class="stat-label-small">本月邀请</text>
                <text class="stat-value-small">{{item.monthInviteCount || 0}}人</text>
              </view>
              <view class="stat-item-small">
                <text class="stat-label-small">订单金额</text>
                <text class="stat-value-small amount">¥{{item.totalOrderAmount || '0.00'}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredUsers.length === 0 && !isLoading}}">
      <view class="empty-icon">📭</view>
      <view class="empty-title">暂无推广用户</view>
      <view class="empty-desc" wx:if="{{filterType === 'all'}}">快去分享您的推广二维码吧！</view>
      <view class="empty-desc" wx:else>
        {{filterType === 'today' ? '今日' : filterType === 'week' ? '本周' : '本月'}}暂无新增推广用户
      </view>
      <view class="empty-action" bindtap="goToPromotion">
        <text class="action-icon">🎯</text>
        <text class="action-text">去推广</text>
      </view>
    </view>
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>
  <!-- 底部操作区 -->
  <view class="bottom-actions">
    <view class="action-btn primary" bindtap="generateQrCode">
      <text class="btn-icon">📱</text>
      <text class="btn-text">生成推广码</text>
    </view>
    <view class="action-btn secondary" bindtap="sharePromotion">
      <text class="btn-icon">📤</text>
      <text class="btn-text">分享推广</text>
    </view>
  </view>
</scroll-view>
<!-- 推广二维码弹窗 -->
<view class="qr-modal" wx:if="{{showQrModal}}">
  <view class="modal-mask" bindtap="closeQrModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">我的推广二维码</text>
      <view class="modal-close" bindtap="closeQrModal">✕</view>
    </view>
    <view class="qr-container">
      <image class="qr-image" src="{{qrCodeUrl}}" mode="aspectFit" wx:if="{{qrCodeUrl}}"></image>
      <view class="qr-loading" wx:else>生成中...</view>
    </view>
    <view class="qr-actions">
      <view class="qr-btn save" bindtap="saveQrCode">保存到相册</view>
      <view class="qr-btn share" bindtap="shareQrCode">分享给好友</view>
    </view>
  </view>
</view>