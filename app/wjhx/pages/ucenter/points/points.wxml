<!-- pages/ucenter/points/points.wxml -->
<wxs module="format" src="../../../utils/format.wxs"></wxs>
<!-- 自定义导航栏 -->
<custom-navbar title="我的积分" gradient-background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" text-color="#ffffff" show-back="{{true}}" bind:back="onNavBack">
  <view slot="right" class="nav-right-content">
    <view class="points-badge">
      <text class="points-icon">💎</text>
      <text class="points-number">{{userPoints || 0}}</text>
    </view>
  </view>
</custom-navbar>
<view class="container" style="padding-top: {{containerPaddingTop}}">
  <!-- 积分概览 -->
  <view class="points-overview">
    <view class="points-display">
      <view class="points-amount">{{userPoints || 0}}</view>
      <view class="points-label">当前积分</view>
    </view>
    <view class="points-desc">
      <view class="desc-item">
        <text class="desc-icon">🛒</text>
        <text class="desc-text">消费{{pointsConfig.earnRate || 1}}元获得1积分</text>
      </view>
      <view class="desc-item">
        <text class="desc-icon">💰</text>
        <text class="desc-text">{{pointsConfig.useRate || 100}}积分抵扣1元</text>
      </view>
    </view>
  </view>
  <!-- 积分规则说明 -->
  <view class="points-rules">
    <view class="rules-title">积分规则</view>
    <view class="rules-content">
      <view class="rule-item">
        <view class="rule-icon">🛒</view>
        <view class="rule-text">购物消费{{pointsConfig.earnRate || 1}}元获得1积分</view>
      </view>
      <view class="rule-item">
        <view class="rule-icon">💰</view>
        <view class="rule-text">{{pointsConfig.useRate || 100}}积分可抵扣1元现金</view>
      </view>
      <view class="rule-item">
        <view class="rule-icon">📝</view>
        <view class="rule-text">最少使用{{pointsConfig.minUsePoints || 100}}积分</view>
      </view>
      <view class="rule-item">
        <view class="rule-icon">⚡</view>
        <view class="rule-text">订单完成后积分立即到账</view>
      </view>
    </view>
  </view>
  <!-- 积分记录 -->
  <view class="points-records">
    <view class="records-title">积分明细</view>
    <view class="records-list" wx:if="{{pointsRecords.length > 0}}">
      <view class="record-item" wx:for="{{pointsRecords}}" wx:key="id">
        <view class="record-left">
          <view class="record-desc">{{item.description}}</view>
          <view class="record-time">{{format.formatDateTime(item.createTime)}}</view>
        </view>
        <view class="record-right">
          <view class="record-points {{item.points > 0 ? 'earn' : 'use'}}">
            {{item.points > 0 ? '+' : ''}}{{item.points}}
          </view>
        </view>
      </view>
    </view>
    <view class="empty-records" wx:else>
      <view class="empty-icon">📝</view>
      <view class="empty-text">暂无积分记录</view>
    </view>
  </view>
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  <view class="load-more" wx:elif="{{!hasMore && pointsRecords.length > 0}}">
    <text>没有更多了</text>
  </view>
</view>