<!-- 管理员用户列表页面 -->
<view class="ns"></view>
<wxs src="../../../../utils/format.wxs" module="format" />
<!-- 自定义导航栏 -->
<view class="custom-navbar">
  <view class="navbar-content">
    <view class="navbar-left" bindtap="goBackToMe">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
    <view class="navbar-title">用户管理</view>
    <view class="navbar-right"></view>
  </view>
</view>
<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索用户昵称、手机号" bindinput="onSearchInput" value="{{searchKeyword}}" />
      <view class="search-btn" bindtap="searchUsers">
        <text>🔍</text>
      </view>
    </view>
    <!-- 类型筛选 -->
    <view class="filter-tabs">
      <view class="tab-item {{currentType === '' ? 'active' : ''}}" bindtap="filterByType" data-type="">
        全部用户
      </view>
      <view class="tab-item {{currentType === 'new' ? 'active' : ''}}" bindtap="filterByType" data-type="new">
        新用户
      </view>
      <view class="tab-item {{currentType === 'active' ? 'active' : ''}}" bindtap="filterByType" data-type="active">
        活跃用户
      </view>
      <view class="tab-item {{currentType === 'vip' ? 'active' : ''}}" bindtap="filterByType" data-type="vip">
        VIP用户
      </view>
      <view class="tab-item {{currentType === 'promoter' ? 'active' : ''}}" bindtap="filterByType" data-type="promoter">
        推广用户
      </view>
    </view>
  </view>
  <!-- 统计信息 -->
  <view class="stats-bar">
    <view class="stat-item">
      <text class="stat-number">{{userStats.totalUsers || 0}}</text>
      <text class="stat-label">总用户</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{userStats.newUsers || 0}}</text>
      <text class="stat-label">今日新增</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{userStats.activeUsers || 0}}</text>
      <text class="stat-label">活跃用户</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{userStats.vipUsers || 0}}</text>
      <text class="stat-label">VIP用户</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{userStats.promoterUsers || 0}}</text>
      <text class="stat-label">推广用户</text>
    </view>
  </view>
  <!-- 用户列表 -->
  <view class="user-list">
    <view class="user-item" wx:for="{{userList}}" wx:key="id">
      <!-- 用户基本信息 -->
      <view class="user-header">
        <image class="user-avatar" src="{{item.avatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
        <view class="user-info">
          <view class="user-name-row">
            <text class="user-name">{{item.nickname || item.username || '未知用户'}}</text>
            <view class="user-level level-{{item.userLevelId || 0}}">
              {{item.userLevelText || 'L0'}}
            </view>
          </view>
          <text class="user-mobile">{{item.mobile || '未绑定手机'}}</text>
          <text class="user-register-time">注册时间：{{format.formatDateTime(item.registerTime)}}</text>
        </view>
        <view class="user-status">
          <view class="status-dot {{item.isOnline ? 'online' : 'offline'}}"></view>
          <text class="status-text">{{item.isOnline ? '在线' : '离线'}}</text>
        </view>
      </view>
      <!-- 用户数据 -->
      <view class="user-data">
        <view class="data-item">
          <text class="data-label">订单数</text>
          <text class="data-value">{{item.orderCount || 0}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">消费金额</text>
          <text class="data-value">¥{{item.totalAmount || '0.00'}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">积分</text>
          <text class="data-value">{{item.points || 0}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">余额</text>
          <text class="data-value">¥{{item.balance || '0.00'}}</text>
        </view>
      </view>
      <!-- 推广信息 -->
      <view class="promotion-info" wx:if="{{item.promotionCount > 0}}">
        <view class="promotion-item clickable" bindtap="viewPromotionDetail" data-user-id="{{item.id}}" data-user-name="{{item.nickname || item.username}}">
          <text class="promotion-label">推广用户：</text>
          <text class="promotion-value">{{item.promotionCount}}人</text>
          <text class="promotion-arrow">›</text>
        </view>
        <view class="promotion-item" wx:if="{{item.promoterId}}">
          <text class="promotion-label">推广者：</text>
          <text class="promotion-value">{{item.promoterName}}</text>
        </view>
      </view>
      <!-- 操作按钮 -->
      <view class="user-actions">
        <view class="action-btn detail-btn" bindtap="viewUserDetail" data-id="{{item.id}}">
          查看详情
        </view>
        <view class="action-btn order-btn" bindtap="viewUserOrders" data-id="{{item.id}}">
          查看订单
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{userList.length === 0 && !loading}}">
      <image class="empty-image" src="/static/images/empty-user.png"></image>
      <text class="empty-text">暂无用户数据</text>
    </view>
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text" wx:if="{{!loadingMore}}">上拉加载更多</text>
      <text class="load-text" wx:else>加载中...</text>
    </view>
    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && userList.length > 0}}">
      <text>没有更多用户了</text>
    </view>
  </view>
  <!-- 加载中 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
<!-- 用户详情弹窗 -->
<view class="user-detail-modal" wx:if="{{showUserDetail}}">
  <view class="modal-mask" bindtap="closeUserDetail"></view>
  <view class="modal-dialog">
    <view class="modal-header">
      <text class="modal-title">用户详情</text>
      <view class="modal-close" bindtap="closeUserDetail">×</view>
    </view>
    <view class="modal-content">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="detail-title">基本信息</view>
        <view class="user-detail-header">
          <image class="detail-avatar" src="{{userDetail.avatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
          <view class="detail-info">
            <text class="detail-name">
              {{userDetail.nickname || userDetail.username || '未知用户'}}
            </text>
            <text class="detail-level">等级：{{userDetail.userLevelText || 'L0'}}</text>
          </view>
        </view>
        <view class="detail-item">
          <text class="detail-label">手机号：</text>
          <text class="detail-value">{{userDetail.mobile || '未绑定'}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">注册时间：</text>
          <text class="detail-value">{{format.formatDateTime(userDetail.registerTime)}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">最后登录：</text>
          <text class="detail-value">{{userDetail.lastLoginTime || '未知'}}</text>
        </view>
      </view>
      <!-- 消费数据 -->
      <view class="detail-section">
        <view class="detail-title">消费数据</view>
        <view class="detail-item">
          <text class="detail-label">订单总数：</text>
          <text class="detail-value">{{userDetail.orderCount || 0}}单</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">消费总额：</text>
          <text class="detail-value">¥{{userDetail.totalAmount || '0.00'}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">当前积分：</text>
          <text class="detail-value">{{userDetail.points || 0}}分</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">账户余额：</text>
          <text class="detail-value">¥{{userDetail.balance || '0.00'}}</text>
        </view>
      </view>
      <!-- 推广数据 -->
      <view class="detail-section" wx:if="{{userDetail.promotionCount > 0 || userDetail.promoterId}}">
        <view class="detail-title">推广数据</view>
        <view class="detail-item" wx:if="{{userDetail.promotionCount > 0}}">
          <text class="detail-label">推广用户：</text>
          <text class="detail-value">{{userDetail.promotionCount}}人</text>
        </view>
        <view class="detail-item" wx:if="{{userDetail.promoterId}}">
          <text class="detail-label">推广者：</text>
          <text class="detail-value">{{userDetail.promoterName}}</text>
        </view>
        <view class="detail-item" wx:if="{{userDetail.promotionEarnings}}">
          <text class="detail-label">推广收益：</text>
          <text class="detail-value">¥{{userDetail.promotionEarnings}}</text>
        </view>
      </view>
    </view>
  </view>
</view>