<!-- 管理员订单列表页面 -->
<view class="ns"></view>
<wxs src="../../../../utils/format.wxs" module="format" />
<!-- 自定义导航栏 -->
<view class="custom-navbar">
  <view class="navbar-content">
    <view class="navbar-left" bindtap="goBackToMe">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
    <view class="navbar-title">订单管理</view>
    <view class="navbar-right"></view>
  </view>
</view>
<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索订单号、用户昵称、商品名称" bindinput="onSearchInput" value="{{searchKeyword}}" />
      <view class="search-btn" bindtap="searchOrders">
        <text>🔍</text>
      </view>
    </view>
    <!-- 状态筛选 -->
    <view class="filter-tabs">
      <view class="tab-item {{currentStatus === '' ? 'active' : ''}}" bindtap="filterByStatus" data-status="">
        全部
      </view>
      <view class="tab-item {{currentStatus === '0' ? 'active' : ''}}" bindtap="filterByStatus" data-status="0">
        待支付
      </view>
      <view class="tab-item {{currentStatus === '1' ? 'active' : ''}}" bindtap="filterByStatus" data-status="1">
        待发货
      </view>
      <view class="tab-item {{currentStatus === '2' ? 'active' : ''}}" bindtap="filterByStatus" data-status="2">
        已发货
      </view>
      <view class="tab-item {{currentStatus === '3' ? 'active' : ''}}" bindtap="filterByStatus" data-status="3">
        已完成
      </view>
    </view>
    <!-- 排序选项 -->
    <view class="sort-section">
      <view class="sort-label">排序：</view>
      <view class="sort-options">
        <view class="sort-item {{sortOrder === 'desc' ? 'active' : ''}}" bindtap="changeSortOrder" data-order="desc">
          <text class="sort-icon">📅</text>
          <text class="sort-text">最新优先</text>
        </view>
        <view class="sort-item {{sortOrder === 'asc' ? 'active' : ''}}" bindtap="changeSortOrder" data-order="asc">
          <text class="sort-icon">🕐</text>
          <text class="sort-text">最早优先</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 订单列表 -->
  <view class="order-list">
    <view class="order-item" wx:for="{{orderList}}" wx:key="id">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-number">订单号：{{item.orderSn}}</text>
          <view class="order-time-wrapper" bindtap="showTimeDetail" data-time="{{item.createTimeDisplay}}" data-order="{{item.orderSn}}">
            <text class="order-time">{{format.formatDateTime(item.createTime)}}</text>
          </view>
        </view>
        <view class="order-status status-{{item.orderStatus}}">{{item.orderStatusText}}</view>
      </view>
      <!-- 用户信息 -->
      <view class="user-info">
        <image class="user-avatar" src="{{item.userAvatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">{{item.userName || '未知用户'}}</text>
          <text class="user-mobile">{{item.userMobile || ''}}</text>
        </view>
      </view>
      <!-- 商品信息 -->
      <view class="goods-list">
        <view class="goods-item" wx:for="{{item.goodsList}}" wx:for-item="goods" wx:key="id">
          <image class="goods-image" src="{{format.formatImageUrl(goods.listPicUrl)}}" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="goods-name">{{goods.goodsName}}</text>
            <text class="goods-spec">{{goods.specifications}}</text>
            <view class="goods-price-qty">
              <text class="goods-price">¥{{goods.price}}</text>
              <text class="goods-qty">×{{goods.number}}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 订单金额和操作按钮 -->
      <view class="order-bottom">
        <view class="order-amount">
          <text class="amount-label">订单金额：</text>
          <text class="amount-value">¥{{item.orderPrice}}</text>
        </view>
        <view class="order-actions">
          <view class="action-btn detail-btn" bindtap="viewOrderDetail" data-id="{{item.id}}">
            查看详情
          </view>
          <view class="action-btn edit-btn" wx:if="{{item.orderStatus == 1}}" bindtap="deliverOrder" data-id="{{item.id}}">
            发货
          </view>
          <view class="action-btn cancel-btn" wx:if="{{item.orderStatus == 0}}" bindtap="cancelOrder" data-id="{{item.id}}">
            取消订单
          </view>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{orderList.length === 0 && !loading}}">
      <image class="empty-image" src="/static/images/empty-order.png"></image>
      <text class="empty-text">暂无订单数据</text>
    </view>
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text" wx:if="{{!loadingMore}}">上拉加载更多</text>
      <text class="load-text" wx:else>加载中...</text>
    </view>
    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多订单了</text>
    </view>
  </view>
  <!-- 加载中 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>