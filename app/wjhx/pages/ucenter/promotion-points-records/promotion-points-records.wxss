/* 推广积分记录页面样式 */
.container {
  height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: 40rpx;
  box-sizing: border-box;
}

/* 头部背景区域 */
.header-background {
  background: linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%);
  padding: 20rpx 30rpx 60rpx;
  margin-top: -20rpx;
  position: relative;
  overflow: hidden;
}

.header-background::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.header-background::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 150rpx;
  height: 150rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

.header-content {
  text-align: center;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  font-weight: 400;
}

/* 积分记录区域 */
.records-section {
  margin: -40rpx 20rpx 20rpx;
  position: relative;
  z-index: 2;
}

/* 积分记录列表 */
.records-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f8f9fa;
}

/* 记录图标 */
.record-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.3);
}

.icon-emoji {
  font-size: 36rpx;
}

/* 记录信息 */
.record-info {
  flex: 1;
}

.record-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.record-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

/* 积分数值 */
.record-points {
  font-size: 32rpx;
  font-weight: 700;
  min-width: 100rpx;
  text-align: right;
}

.record-points.positive {
  color: #4CAF50;
}

.record-points.negative {
  color: #f44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 18rpx rgba(66, 165, 245, 0.3);
}

.action-icon {
  margin-right: 8rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #42A5F5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #42A5F5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: #666;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}