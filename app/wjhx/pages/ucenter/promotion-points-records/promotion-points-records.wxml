<!-- 推广积分记录页面 -->
<wxs module="format" src="../../../utils/format.wxs"></wxs>

<!-- 自定义导航栏 -->
<custom-navbar title="推广积分记录" gradient-background="linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%)" text-color="#ffffff" opacity="{{navOpacity}}" bind:back="onNavBack">
</custom-navbar>

<scroll-view class="container" style="padding-top: {{navbarHeight}}px;" scroll-y="{{true}}" bindscroll="onPageScroll" enhanced="{{true}}" show-scrollbar="{{false}}" enable-back-to-top="{{true}}">
  <!-- 头部背景区域 -->
  <view class="header-background">
    <view class="header-content">
      <view class="header-title">推广积分记录</view>
      <view class="header-subtitle">查看您的推广积分获得和使用记录</view>
    </view>
  </view>

  <!-- 积分记录列表 -->
  <view class="records-section">
    <view class="records-list" wx:if="{{records.length > 0}}">
      <view class="record-item" wx:for="{{records}}" wx:key="id">
        <view class="record-icon">
          <text class="icon-emoji">{{item.typeIcon}}</text>
        </view>
        <view class="record-info">
          <view class="record-title">{{item.typeName}}</view>
          <view class="record-desc" wx:if="{{item.description}}">{{item.description}}</view>
          <view class="record-time">{{item.createTimeFormatted}}</view>
        </view>
        <view class="record-points {{item.points > 0 ? 'positive' : 'negative'}}">
          {{item.pointsDisplay}}
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{isEmpty && !isLoading}}">
      <view class="empty-icon">📊</view>
      <view class="empty-title">暂无积分记录</view>
      <view class="empty-desc">您还没有推广积分记录，快去推广获得积分吧！</view>
      <view class="empty-action" bindtap="goToPromotion">
        <text class="action-icon">🎯</text>
        <text class="action-text">去推广</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading && records.length === 0}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{isLoading && records.length > 0}}">
      <view class="loading-spinner-small"></view>
      <text class="load-more-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && records.length > 0}}">
      <text class="no-more-text">没有更多记录了</text>
    </view>
  </view>
</scroll-view>