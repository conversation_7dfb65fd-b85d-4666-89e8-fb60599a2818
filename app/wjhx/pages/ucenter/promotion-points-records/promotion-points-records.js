const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    records: [],
    isLoading: true,
    isEmpty: false,
    page: 1,
    hasMore: true,

    // 自定义导航栏相关
    navOpacity: 0.95,
    navbarHeight: 0,
  },

  onLoad: function (options) {
    this.initNavbar();
    this.loadPromotionPointsRecords();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.setData({
      page: 1,
      hasMore: true,
      records: []
    });
    this.loadPromotionPointsRecords();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadPromotionPointsRecords();
    }
  },

  /**
   * 加载推广积分记录
   */
  loadPromotionPointsRecords: function () {
    const that = this;
    const page = that.data.page;
    
    that.setData({
      isLoading: true
    });

    util.request(api.GetPromotionPointsRecords, {
      page: page,
      size: 20
    }).then(res => {
      console.log('推广积分记录:', res);
      
      if (res.success && res.data) {
        const newRecords = res.data.records || [];
        const formattedRecords = newRecords.map(record => ({
          ...record,
          createTimeFormatted: that.formatTime(new Date(record.createTime)),
          pointsDisplay: record.points > 0 ? `+${record.points}` : record.points.toString(),
          typeIcon: that.getRecordIcon(record.type),
          typeName: that.getRecordTypeName(record.type)
        }));

        const allRecords = page === 1 ? formattedRecords : that.data.records.concat(formattedRecords);
        
        that.setData({
          records: allRecords,
          isEmpty: allRecords.length === 0,
          isLoading: false,
          page: page + 1,
          hasMore: newRecords.length >= 20
        });
      } else {
        that.setData({
          isLoading: false,
          isEmpty: that.data.records.length === 0
        });
        if (page === 1) {
          wx.showToast({
            title: res.msg || '加载失败',
            icon: 'none'
          });
        }
      }
    }).catch(err => {
      console.error('加载推广积分记录失败:', err);
      that.setData({
        isLoading: false,
        isEmpty: that.data.records.length === 0
      });
      if (page === 1) {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime: function (date) {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    const hours = Math.floor(diff / (60 * 60 * 1000));
    const minutes = Math.floor(diff / (60 * 1000));

    if (days > 0) {
      if (days === 1) return '昨天';
      if (days < 7) return `${days}天前`;
      return `${date.getMonth() + 1}-${date.getDate()}`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  /**
   * 获取记录类型图标
   */
  getRecordIcon: function(type) {
    const iconMap = {
      'promotion': '👥', // 推广奖励
      'level_upgrade': '⬆️', // 等级升级
      'bonus': '🎁', // 额外奖励
      'deduction': '➖', // 扣除
      'other': '📝' // 其他
    };
    return iconMap[type] || '📝';
  },

  /**
   * 获取记录类型名称
   */
  getRecordTypeName: function(type) {
    const nameMap = {
      'promotion': '推广奖励',
      'level_upgrade': '等级升级奖励',
      'bonus': '额外奖励',
      'deduction': '积分扣除',
      'other': '其他'
    };
    return nameMap[type] || '其他';
  },

  /**
   * 去推广
   */
  goToPromotion: function() {
    wx.navigateBack();
  },

  /**
   * 初始化导航栏
   */
  initNavbar: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44;
    const navbarHeight = statusBarHeight + titleBarHeight;

    this.setData({
      navbarHeight: navbarHeight
    });
  },

  /**
   * 导航栏返回事件
   */
  onNavBack: function() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/ucenter/me/me'
      });
    }
  },

  /**
   * 页面滚动事件
   */
  onPageScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));

    if (Math.abs(opacity - this.data.navOpacity) > 0.02) {
      this.setData({
        navOpacity: opacity
      });
    }
  }
});