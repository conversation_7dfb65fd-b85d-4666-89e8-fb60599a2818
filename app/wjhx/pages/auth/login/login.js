var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var app = getApp();
var backUrl = '/pages/index/index';
var backParams = {};
Page({
  data: {
    backUrl: '/pages/index/index',
    backParams: '',
    isLoading: false,
    // 新增字段
    isLoggedIn: false,           // 是否已完成微信登录
    isProfileComplete: false,    // 是否已完成个人信息填写
    avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0', // 默认头像
    nickname: '',                // 用户昵称
    canComplete: false,          // 是否可以完成注册
    tempUserInfo: null,          // 临时存储的用户信息
    code: null                   // 微信登录凭证
  },
  onLoad: function (options) {
    console.log('登录页面加载，接收参数:', options);

    // 处理推广场景值
    this.handlePromotionScene(options);

    // 优先级：URL参数 > localStorage > 默认首页

    // 1. 首先尝试从URL参数获取
    if (options.backUrl) {
      backUrl = decodeURIComponent(options.backUrl);
      console.log('从URL参数获取backUrl:', backUrl);

      if (options.backParamJson) {
        try {
          const decodedParam = decodeURIComponent(options.backParamJson);
          console.log('解码后的backParamJson:', decodedParam);

          if (decodedParam.trim() === '') {
            console.log('backParamJson为空，跳过解析');
            backParams = '';
          } else {
            let obj = JSON.parse(decodedParam);
            if (obj && typeof obj === 'object') {
              backParams = Object.keys(obj).map(function (key) {
                return encodeURIComponent(key) + "=" + encodeURIComponent(obj[key]);
              }).join("&");
              console.log('从URL参数解析backParams:', backParams);
            } else {
              console.log('解析的对象无效:', obj);
              backParams = '';
            }
          }
        } catch (e) {
          console.error('解析URL参数中的backParamJson失败:', e);
          console.error('原始参数:', options.backParamJson);
          backParams = '';
        }
      }
    }
    // 2. 如果URL参数没有，则从localStorage获取
    else {
      let storedBackUrl = wx.getStorageSync('backUrl');
      let storedBackParams = wx.getStorageSync('backParamJson');

      if (storedBackUrl) {
        backUrl = storedBackUrl;
        console.log('从localStorage获取backUrl:', backUrl);

        try {
          if (storedBackParams) {
            console.log('localStorage中的backParams:', storedBackParams, '类型:', typeof storedBackParams);

            let obj;
            if (typeof storedBackParams === 'string') {
              if (storedBackParams.trim() === '') {
                console.log('localStorage中的backParams为空字符串');
                backParams = '';
                return;
              }
              obj = JSON.parse(storedBackParams);
            } else if (typeof storedBackParams === 'object' && storedBackParams !== null) {
              obj = storedBackParams;
            } else {
              console.log('localStorage中的backParams类型不支持:', typeof storedBackParams);
              backParams = '';
              return;
            }

            if (obj && typeof obj === 'object') {
              backParams = Object.keys(obj).map(function (key) {
                return encodeURIComponent(key) + "=" + encodeURIComponent(obj[key]);
              }).join("&");
              console.log('从localStorage解析backParams:', backParams);
            } else {
              console.log('解析的对象无效:', obj);
              backParams = '';
            }
          }
        } catch (e) {
          console.error('解析localStorage中的backParamJson失败:', e);
          console.error('原始参数:', storedBackParams);
          backParams = '';
        }

        // 使用后清除localStorage
        wx.removeStorageSync('backUrl');
        wx.removeStorageSync('backParamJson');
      }
    }

    console.log('最终确定的返回页面:', { backUrl, backParams });

    // 更新页面数据，显示返回页面信息
    this.setData({
      backUrl: backUrl,
      backParams: backParams,
      // 确保初始状态正确
      isLoggedIn: false,
      isProfileComplete: false,
      canComplete: false
    });

    console.log('页面初始化完成，当前状态:', this.data);
  },
  onReady: function () {

  },
  onShow: function () {
    // 页面显示时检查是否已经登录
    this.checkExistingLogin();
  },

  /**
   * 检查是否已经存在登录状态
   */
  checkExistingLogin: function() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    console.log('检查现有登录状态:', { userInfo, token });

    if (userInfo && token) {
      // 用户已经登录，直接跳转
      console.log('用户已登录，直接跳转');
      wx.showToast({
        title: '您已登录',
        icon: 'success',
        duration: 1000
      });

      setTimeout(() => {
        util.toPage(this.data.backUrl, this.data.backParams);
      }, 1000);
    } else {
      // 确保显示登录界面
      this.setData({
        isLoggedIn: false,
        isProfileComplete: false
      });
    }
  },
  onHide: function () {
    // 页面隐藏

  },
  onUnload: function () {
    // 页面关闭

  },

  /**
   * 获取推广场景参数
   */
  getPromotionScene: function() {
    // 1. 优先从本地存储获取推广者ID（首页已处理推广参数）
    const promoterId = wx.getStorageSync('promoter_id');
    if (promoterId) {
      console.log('从本地存储获取推广者ID:', promoterId);
      return promoterId;
    }

    // 2. 从页面参数获取（如果有的话）
    if (this.data.backParams) {
      try {
        console.log('准备解析页面参数:', this.data.backParams, '类型:', typeof this.data.backParams);

        let params;

        // 检查参数类型
        if (typeof this.data.backParams === 'string') {
          // 如果是字符串，尝试解析JSON
          if (this.data.backParams.trim() === '') {
            console.log('页面参数为空字符串，跳过解析');
            return null;
          }
          params = JSON.parse(this.data.backParams);
        } else if (typeof this.data.backParams === 'object' && this.data.backParams !== null) {
          // 如果已经是对象，直接使用
          params = this.data.backParams;
        } else {
          console.log('页面参数类型不支持:', typeof this.data.backParams);
          return null;
        }

        console.log('解析后的页面参数:', params);

        if (params && typeof params === 'object') {
          if (params.promo) {
            console.log('从页面参数获取推广者ID:', params.promo);
            return params.promo;
          }
          if (params.scene && typeof params.scene === 'string' && params.scene.startsWith('promo_')) {
            const promoterId = params.scene.replace('promo_', '');
            console.log('从场景值获取推广者ID:', promoterId);
            return promoterId;
          }
        }
      } catch (e) {
        console.error('解析页面参数失败:', e);
        console.error('原始参数值:', this.data.backParams);
        console.error('参数类型:', typeof this.data.backParams);
      }
    }

    return null;
  },

  /**
   * 智能微信登录：检查用户信息完整性
   */
  doWechatLogin: function () {
    const that = this;
    wx.showLoading({
      title: '登录中...',
    });

    util.login().then((code) => {
      console.log('获取微信登录凭证成功:', code);

      // 先尝试用空的用户信息登录，检查用户是否已存在且信息完整
      that.tryLoginWithExistingInfo(code);

    }).catch(err => {
      wx.hideLoading();
      console.error('获取微信登录凭证失败:', err);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 尝试用现有信息登录
   */
  tryLoginWithExistingInfo: function(code) {
    const that = this;

    // 获取推广参数
    const promotionScene = that.getPromotionScene();

    // 构造一个空的用户信息，用于检查用户是否已存在
    const emptyUserInfo = {
      userInfo: {
        nickName: '',
        avatarUrl: '',
        gender: 0,
        language: 'zh_CN',
        city: '',
        province: '',
        country: 'China'
      }
    };

    util.request(api.AuthLoginByWeixin, {
      code: code,
      userInfo: emptyUserInfo,
      promotionScene: promotionScene
    }, 'POST').then(res => {
      console.log('登录检查返回数据:', res);

      // 兼容多种返回格式的判断
      const isSuccess = res.success !== false && (res.code == 200 || res.errno === 0 || res.status == 200);

      if (isSuccess && res.data) {
        const userInfo = res.data.userInfo || res.data;
        const token = res.data.token;

        console.log('返回的用户信息:', userInfo);

        if (userInfo && token) {
          // 检查用户信息是否完整和有效
          const isValidNickname = that.isValidNickname(userInfo.nickname || userInfo.username);
          const isValidAvatar = that.isValidAvatar(userInfo.avatar);

          console.log('用户信息完整性检查:', {
            nickname: userInfo.nickname || userInfo.username,
            avatar: userInfo.avatar,
            isValidNickname,
            isValidAvatar
          });

          if (isValidNickname && isValidAvatar) {
            // 用户信息完整且有效，直接登录成功
            that.completeLoginSuccess(userInfo, token);
          } else {
            // 用户信息不完整或无效，需要完善信息
            wx.hideLoading();
            that.setData({
              isLoggedIn: true,
              isProfileComplete: false,
              code: code,
              // 预填充已有信息（如果有效的话）
              nickname: isValidNickname ? (userInfo.nickname || userInfo.username) : '',
              avatarUrl: isValidAvatar ? userInfo.avatar : 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
            });

            // 检查完成条件
            that.checkCanComplete();

            wx.showToast({
              title: '请完善个人信息',
              icon: 'none',
              duration: 1500
            });
          }
        } else {
          wx.hideLoading();
          console.error('登录数据不完整:', { userInfo, token });
          wx.showToast({
            title: '登录数据异常',
            icon: 'none',
            duration: 2000
          });
        }
      } else {
        wx.hideLoading();
        console.error('登录失败:', res);
        wx.showToast({
          title: res.msg || res.message || '登录失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('登录请求失败:', err);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 完成登录成功的处理
   */
  completeLoginSuccess: function(userInfo, token) {
    const that = this;

    wx.hideLoading();

    // 保存用户信息和token
    try {
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('token', token);
      console.log('用户信息和Token保存成功');
    } catch (e) {
      console.error('保存用户信息失败:', e);
      wx.showToast({
        title: '登录信息保存失败',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 更新全局数据
    const app = getApp();
    app.globalData.userInfo = userInfo;
    app.globalData.token = token;

    // 检查是否有推广参数需要处理
    that.handlePromotionAfterLogin();

    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1000
    });

    // 缩短延迟时间，确保快速跳转
    setTimeout(() => {
      console.log('登录成功，准备跳转到:', {
        backUrl: that.data.backUrl,
        backParams: that.data.backParams
      });

      // 验证登录状态是否正确保存
      const savedUserInfo = wx.getStorageSync('userInfo');
      const savedToken = wx.getStorageSync('token');

      if (!savedUserInfo || !savedToken) {
        console.error('登录信息保存验证失败');
        wx.showToast({
          title: '登录状态异常，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      util.toPage(that.data.backUrl, that.data.backParams);
    }, 1000);
  },

  /**
   * 验证昵称是否有效
   */
  isValidNickname: function(nickname) {
    if (!nickname || typeof nickname !== 'string') {
      return false;
    }

    const trimmedNickname = nickname.trim();

    // 检查是否为空
    if (trimmedNickname === '') {
      return false;
    }

    // 检查是否为默认的"微信用户"
    if (trimmedNickname === '微信用户') {
      return false;
    }

    // 检查是否为其他常见的默认昵称
    const defaultNicknames = ['微信用户', 'WeChat User', 'User', '用户', '默认用户'];
    if (defaultNicknames.includes(trimmedNickname)) {
      return false;
    }

    return true;
  },

  /**
   * 验证头像是否有效
   */
  isValidAvatar: function(avatar) {
    if (!avatar || typeof avatar !== 'string') {
      return false;
    }

    const trimmedAvatar = avatar.trim();

    // 检查是否为空
    if (trimmedAvatar === '') {
      return false;
    }

    // 检查是否为默认头像地址
    const defaultAvatarUrls = [
      'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uBMGjxKBKlNHAMX3oP1WdGjHFAAjGzQjjdAHuj7aCnLs3U0sSNPibeHYicaKld1HyHIehs7iaQcaA/132',
      '/static/images/sicon/customer.png'
    ];

    // 检查是否为默认头像
    if (defaultAvatarUrls.includes(trimmedAvatar)) {
      return false;
    }

    // 检查是否为有效的URL格式
    try {
      // 简单的URL格式检查
      if (trimmedAvatar.startsWith('http://') ||
          trimmedAvatar.startsWith('https://') ||
          trimmedAvatar.startsWith('wxfile://') ||
          trimmedAvatar.startsWith('/')) {
        return true;
      }
    } catch (e) {
      console.error('头像URL格式检查失败:', e);
    }

    return false;
  },

  /**
   * 头像选择回调
   */
  onChooseAvatar: function (e) {
    console.log('头像选择事件触发:', e);
    const { avatarUrl } = e.detail;
    console.log('选择头像:', avatarUrl);

    this.setData({
      avatarUrl: avatarUrl
    });

    console.log('头像设置后的数据:', this.data);
    this.checkCanComplete();
  },

  /**
   * 昵称输入回调
   */
  onNicknameInput: function (e) {
    console.log('昵称输入事件触发:', e);
    const nickname = e.detail.value;
    console.log('输入昵称:', nickname);

    this.setData({
      nickname: nickname
    });

    console.log('昵称设置后的数据:', this.data);
    this.checkCanComplete();
  },

  /**
   * 昵称失焦回调
   */
  onNicknameBlur: function (e) {
    const nickname = e.detail.value;
    console.log('昵称输入完成:', nickname);

    this.setData({
      nickname: nickname
    });

    this.checkCanComplete();
  },

  /**
   * 检查是否可以完成注册
   */
  checkCanComplete: function () {
    const { avatarUrl, nickname } = this.data;

    // 使用新的验证方法检查头像和昵称是否有效
    const isValidAvatar = this.isValidAvatar(avatarUrl);
    const isValidNickname = this.isValidNickname(nickname);

    console.log('检查完成条件:', {
      avatarUrl,
      nickname,
      isValidAvatar,
      isValidNickname,
      canComplete: isValidAvatar && isValidNickname
    });

    this.setData({
      canComplete: isValidAvatar && isValidNickname
    });
  },

  /**
   * 完成个人信息填写，提交到服务器
   */
  completeProfile: function () {
    const that = this;
    const { avatarUrl, nickname } = this.data;

    wx.showLoading({
      title: '注册中...',
    });

    // 重新获取微信登录凭证，因为之前的code已经被使用过了
    util.login().then((newCode) => {
      console.log('重新获取微信登录凭证成功:', newCode);

      // 获取推广参数
      const promotionScene = that.getPromotionScene();

      // 构造用户信息对象，模拟旧版 getUserProfile 的数据结构
      const userInfo = {
        userInfo: {
          nickName: nickname,
          avatarUrl: avatarUrl,
          gender: 0, // 默认未知
          language: 'zh_CN',
          city: '',
          province: '',
          country: 'China'
        }
      };

      util.request(api.AuthLoginByWeixin, {
        code: newCode,  // 使用新的code
        userInfo: userInfo,
        promotionScene: promotionScene
      }, 'POST').then(res => {
        wx.hideLoading();
        console.log('注册接口返回数据:', res);

        // 兼容多种返回格式的判断
        const isSuccess = res.success !== false && (res.code == 200 || res.errno === 0 || res.status == 200);

        if (isSuccess && res.data) {
          // 保存用户信息和token
          const userInfo = res.data.userInfo || res.data;
          const token = res.data.token;

          console.log('准备保存的用户信息:', userInfo);
          console.log('准备保存的token:', token);

          if (userInfo && token) {
            wx.setStorageSync('userInfo', userInfo);
            wx.setStorageSync('token', token);

            // 更新全局数据
            const app = getApp();
            app.globalData.userInfo = userInfo;
            app.globalData.token = token;

            // 标记个人信息已完成
            that.setData({
              isProfileComplete: true
            });

            // 检查是否有推广参数需要处理
            that.handlePromotionAfterLogin();

            wx.showToast({
              title: '注册成功',
              icon: 'success',
              duration: 1500
            });

            // 延迟跳转，确保数据保存完成
            setTimeout(() => {
              console.log('注册成功，准备跳转到:', { backUrl: that.data.backUrl, backParams: that.data.backParams });

              // 使用优化后的跳转方法
              util.toPage(that.data.backUrl, that.data.backParams);
            }, 1500);
          } else {
            console.error('注册数据不完整:', { userInfo, token });
            wx.showToast({
              title: '注册数据异常',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          console.error('注册失败:', res);
          wx.showToast({
            title: res.msg || res.message || '注册失败',
            icon: 'none',
            duration: 2000
          });
        }
      }).catch(err => {
        wx.hideLoading();
        console.error('注册请求失败:', err);
        wx.showToast({
          title: '注册失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });

    }).catch(err => {
      wx.hideLoading();
      console.error('重新获取微信登录凭证失败:', err);
      wx.showToast({
        title: '获取登录凭证失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  navigateBack: function (e) {
    if (getCurrentPages().length > 1) {
      wx.navigateBack({});
    } else {
      wx.switchTab({
        url: '/pages/index/index',
      })
    }
  },

  /**
   * 处理推广场景值
   */
  handlePromotionScene: function(options) {
    // 处理小程序码场景值
    if (options.scene) {
      console.log('登录页面检测到场景值:', options.scene);

      // 解析场景值，格式为 promo_用户ID
      if (options.scene.startsWith('promo_')) {
        const promoterId = options.scene.replace('promo_', '');
        console.log('从场景值解析推广者ID:', promoterId);
        wx.setStorageSync('promoter_id', promoterId);

        // 显示推广提示
        wx.showToast({
          title: '欢迎通过好友推广进入',
          icon: 'none',
          duration: 2000
        });
      }
    }

    // 处理直接推广参数
    if (options.promo) {
      console.log('登录页面检测到推广参数:', options.promo);
      wx.setStorageSync('promoter_id', options.promo);

      // 显示推广提示
      wx.showToast({
        title: '欢迎通过好友推广进入',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 登录成功后处理推广关系
   */
  handlePromotionAfterLogin: function() {
    const promoterId = wx.getStorageSync('promoter_id');

    if (promoterId) {
      console.log('登录成功后检测到推广参数，尝试建立推广关系:', promoterId);

      // 构建推广场景值
      const promotionScene = promoterId.toString().startsWith('promo_') ? promoterId : 'promo_' + promoterId;

      util.request(api.EstablishPromotionRelation, {
        promotionScene: promotionScene
      }, 'POST').then(res => {
        if (res.errno === 0 || res.code === 200) {
          console.log('登录后推广关系建立成功:', res.errmsg || res.msg);
          wx.showToast({
            title: '欢迎通过好友推广加入！',
            icon: 'success',
            duration: 2000
          });
        } else {
          console.log('登录后推广关系建立失败:', res.errmsg || res.msg);
          // 如果是已有推广者的情况，不显示错误提示
          if (!(res.errmsg || res.msg || '').includes('已有推广者')) {
            wx.showToast({
              title: '推广关系处理异常',
              icon: 'none',
              duration: 1500
            });
          }
        }
        // 无论成功失败，都清除推广参数，避免重复处理
        wx.removeStorageSync('promoter_id');
      }).catch(err => {
        console.error('登录后建立推广关系请求失败:', err);
        // 请求失败也清除推广参数
        wx.removeStorageSync('promoter_id');
      });
    }
  },

  /**
   * 安全的JSON解析工具
   */
  safeJsonParse: function(jsonString, defaultValue = null) {
    try {
      if (!jsonString) {
        console.log('JSON字符串为空或undefined');
        return defaultValue;
      }

      // 如果已经是对象，直接返回
      if (typeof jsonString === 'object' && jsonString !== null) {
        console.log('参数已经是对象，直接返回');
        return jsonString;
      }

      // 如果是字符串，尝试解析
      if (typeof jsonString === 'string') {
        const trimmed = jsonString.trim();
        if (trimmed === '') {
          console.log('JSON字符串为空');
          return defaultValue;
        }

        const parsed = JSON.parse(trimmed);
        console.log('JSON解析成功:', parsed);
        return parsed;
      }

      console.log('不支持的JSON参数类型:', typeof jsonString);
      return defaultValue;

    } catch (e) {
      console.error('JSON解析失败:', e);
      console.error('原始数据:', jsonString);
      console.error('数据类型:', typeof jsonString);
      return defaultValue;
    }
  }
})
