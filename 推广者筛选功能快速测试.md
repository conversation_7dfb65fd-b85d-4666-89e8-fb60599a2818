# 推广者筛选功能快速测试指南

## 测试前准备

### 1. 确保测试数据
确保推广详情页面有以下类型的测试用户：
- **有推广者的用户**: 至少2-3个用户有 `promoterId` 字段
- **无推广者的用户**: 至少2-3个用户 `promoterId` 为空或null
- **不同时间的用户**: 包含今日、本周、本月的不同时间注册的用户

### 2. 页面访问
进入推广详情页面：`pages/ucenter/promotion-detail/promotion-detail`

## 快速测试步骤

### 测试1：基础显示检查
1. **进入页面**
   - 确认显示两个筛选区域：
     - "时间筛选："区域
     - "推广者筛选："区域
   
2. **默认状态检查**
   - 时间筛选默认选中"全部"
   - 推广者筛选默认选中"全部用户"
   - 用户列表显示所有用户

3. **用户信息显示**
   - 每个用户显示推广者信息行
   - 有推广者：显示"推广者：[推广者昵称]" + 👑 图标
   - 无推广者：显示"推广者：直接注册" + 🆓 图标

### 测试2：推广者筛选功能
1. **筛选"有推广者"**
   - 点击"有推广者"筛选项
   - 确认筛选项变为蓝色激活状态
   - 确认列表只显示有推广者的用户
   - 确认状态提示显示"显示有推广者"

2. **筛选"无推广者"**
   - 点击"无推广者"筛选项
   - 确认筛选项变为蓝色激活状态
   - 确认列表只显示无推广者的用户
   - 确认状态提示显示"显示无推广者"

3. **返回"全部用户"**
   - 点击"全部用户"筛选项
   - 确认显示所有用户
   - 确认状态提示显示"显示全部用户"

### 测试3：组合筛选功能
1. **时间 + 推广者筛选**
   - 选择"今日" + "有推广者"
   - 确认只显示今日注册且有推广者的用户
   - 确认状态提示显示"显示今日有推广者"

2. **其他组合测试**
   - 测试"本周" + "无推广者"
   - 确认状态提示显示"显示本周无推广者"
   - 测试"本月" + "有推广者"
   - 确认状态提示显示"显示本月有推广者"

### 测试4：视觉效果检查
1. **筛选器样式**
   - 时间筛选器激活时显示橙色背景
   - 推广者筛选器激活时显示蓝色背景
   - 筛选器标签清晰显示

2. **用户信息样式**
   - 有推广者用户的推广者信息显示为蓝色
   - 无推广者用户的推广者信息显示为灰色
   - 👑 和 🆓 图标正确显示

3. **状态提示样式**
   - 状态提示区域有蓝色背景
   - 用户数量统计显示在右侧
   - 排序信息显示在下方

### 测试5：交互体验检查
1. **筛选器交互**
   - 点击筛选项有视觉反馈
   - 筛选结果实时更新
   - 状态提示实时变化

2. **排序功能兼容性**
   - 在筛选状态下测试排序功能
   - 确认排序不影响筛选结果
   - 确认筛选不影响排序功能

## 预期结果

### 功能正常标准
- ✅ 推广者筛选器正确显示和工作
- ✅ 筛选结果准确无误
- ✅ 状态提示文本正确
- ✅ 用户信息显示完整
- ✅ 组合筛选功能正常
- ✅ 视觉样式符合设计
- ✅ 交互体验流畅

### 常见问题排查
1. **筛选结果不正确**
   - 检查用户数据中的 `promoterId` 字段
   - 确认筛选逻辑是否正确执行

2. **状态提示不更新**
   - 检查 `getFilterStatusText` 方法是否正确调用
   - 确认数据绑定是否正确

3. **样式显示异常**
   - 检查CSS类名是否正确
   - 确认条件渲染逻辑

4. **推广者信息不显示**
   - 检查用户数据结构
   - 确认 `promoterNickname` 字段

## 测试完成确认

当以下所有项目都通过测试时，推广者筛选功能测试完成：

- [ ] 筛选器UI正确显示
- [ ] "有推广者"筛选功能正常
- [ ] "无推广者"筛选功能正常
- [ ] "全部用户"筛选功能正常
- [ ] 时间+推广者组合筛选正常
- [ ] 状态提示文本准确
- [ ] 用户推广者信息正确显示
- [ ] 视觉样式符合设计
- [ ] 交互体验流畅
- [ ] 排序功能兼容性正常

## 测试数据示例

如果需要测试数据，可以确保用户数据包含以下字段：
```javascript
{
  id: "用户ID",
  nickname: "用户昵称",
  avatar: "用户头像",
  promoterId: "推广者ID（有推广者时不为空）",
  promoterNickname: "推广者昵称",
  promotionTime: "推广时间",
  // ... 其他统计字段
}
```

测试完成后，推广者筛选功能应该能够帮助用户有效区分和管理不同来源的推广用户。