# 商品评价星级修复测试指南

## 问题描述
商品评价时，评价录入的星级与展示的不符。前端可以选择1-5星，但后端没有保存星级数据。

## 修复内容

### 1. 数据库修改
- 在 `weshop_comment` 表中添加 `rating` 字段（TINYINT类型，默认值5）
- 执行SQL: `database/add_rating_column.sql`

### 2. 后端代码修改
- **Comment实体类**: 添加 `rating` 字段及getter/setter方法
- **CommentPostVO**: 添加 `rating` 字段，在toPO()方法中设置默认值5星
- **CommentResultVO**: 添加 `rating` 字段，用于返回评价数据时包含星级
- **CommentMapper.xml**: 在查询和映射中添加 `rating` 字段

### 3. 前端代码
- 前端代码无需修改，已经正确发送 `rating` 字段

## 测试步骤

### 1. 数据库准备
```sql
-- 执行数据库修改
source database/add_rating_column.sql;

-- 验证字段添加成功
DESCRIBE weshop_comment;
```

### 2. 后端测试
1. 重启后端服务
2. 检查编译是否成功
3. 查看日志确认无错误

### 3. 功能测试

#### 3.1 评价录入测试
1. 登录小程序
2. 进入订单详情页面
3. 点击"评价"按钮
4. 在评价页面：
   - 选择不同的星级（1-5星）
   - 输入评价内容
   - 可选择上传图片
   - 点击"提交评价"
5. 验证提交成功

#### 3.2 评价展示测试
1. 在商品详情页查看评价列表
2. 验证星级显示是否正确
3. 检查星级是否与录入时一致

#### 3.3 数据库验证
```sql
-- 查看最新的评价记录
SELECT id, rating, content, create_time 
FROM weshop_comment 
ORDER BY create_time DESC 
LIMIT 10;
```

### 4. 边界测试
1. 测试默认5星评价
2. 测试1星评价
3. 测试不选择星级的情况（应该默认5星）

## 预期结果
1. 用户选择的星级能正确保存到数据库
2. 评价展示时星级与录入时一致
3. 默认情况下显示5星评价
4. 历史评价数据自动设置为5星

## 注意事项
1. 确保数据库修改在生产环境执行前进行备份
2. 历史评价数据会自动设置为5星评价
3. 前端星级显示组件需要使用返回的rating字段

## 回滚方案
如果出现问题，可以执行以下SQL回滚：
```sql
-- 删除rating字段
ALTER TABLE weshop_comment DROP COLUMN rating;
```