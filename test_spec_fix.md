# 商品规格切换问题修复测试

## 问题描述
在编辑场景下单规格切换多规格时，添加规格值时，商品属性下面的记录条不增加。

## 修复内容

### 1. 修改 `addOneAttr` 方法
- 在编辑模式下特殊处理规格值添加
- 保存当前批量设置行数据
- 重新生成属性组合后恢复批量设置数据
- 强制更新表格组件

### 2. 优化 `generateAttr` 方法
- 保存现有的批量设置行数据
- 确保在重新生成时不丢失用户设置的数据
- 添加更完善的错误处理和数据恢复机制

### 3. 改进 `changeSpec` 方法
- 在切换到多规格时正确初始化数据结构
- 确保 manyFormValidate 有正确的批量设置行
- 强制更新表格组件

### 4. 增强 `createAttr` 方法
- 添加调试信息便于问题排查
- 确保在编辑模式下正确处理规格值添加

### 5. 添加监听器和调试信息
- 在 SpecStock 组件中添加数据变化监听
- 添加详细的调试日志便于问题定位

## 测试步骤

1. **进入商品编辑页面**
   - 选择一个已有的商品进行编辑
   - 确保商品当前是单规格

2. **切换到多规格**
   - 点击规格类型切换到"多规格"
   - 检查是否正确显示商品属性表格

3. **添加规格**
   - 点击"添加新规格"按钮
   - 输入规格名称（如：颜色）
   - 确认规格添加成功

4. **添加规格值**
   - 点击"添加规格值"
   - 输入规格值（如：红色）
   - 确认规格值添加成功
   - **重点检查：商品属性表格是否增加了新的记录行**

5. **继续添加规格值**
   - 再次添加规格值（如：蓝色）
   - 确认每次添加都会在商品属性表格中增加对应的记录行

6. **验证数据完整性**
   - 检查批量设置行是否保持不变
   - 检查已设置的价格、库存等信息是否丢失
   - 检查表格显示是否正常

## 预期结果

- 在编辑模式下从单规格切换到多规格后，添加规格值时商品属性表格应该正确增加记录行
- 批量设置行的数据应该保持不变
- 表格应该正确渲染和更新
- 不应该出现数据丢失或显示异常

## 调试信息

修复后的代码会在浏览器控制台输出详细的调试信息，包括：
- 方法调用时的参数
- 数据变化过程
- 表格更新状态
- 组合生成结果

可以通过这些信息来验证修复是否生效。
