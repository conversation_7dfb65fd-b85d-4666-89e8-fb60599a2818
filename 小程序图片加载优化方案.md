# 小程序图片加载优化方案

## 问题分析

经过代码审查，发现小程序每次页面跳转都会加载大量图片的主要原因：

### 1. 首页轮播图重复加载
- 首页 `index.js` 的 `onShow()` 方法会重新调用 `fetchBannerData()`
- 轮播组件会同时渲染当前、前一张、后一张图片
- 每次页面显示都重新获取商品数据作为轮播图

### 2. 商品列表页面批量加载
- `allGoods` 页面一次性加载20个商品的图片
- 虽然使用了 `lazy-load`，但可视区域内的图片仍会立即加载

### 3. 缺乏图片缓存机制
- 没有图片预加载和缓存策略
- 每次页面跳转都重新请求图片资源

## 优化方案

### 1. 首页轮播图优化

#### 修改 `app/wjhx/pages/index/index.js`：

```javascript
// 在 onShow 方法中添加缓存判断
onShow() {
  console.log('页面显示');
  
  // 只有在页面已加载且轮播图数据为空时才刷新
  if (this.data.pageLoaded && this.data.banner.length === 0) {
    this.fetchBannerData();
  }
},

// 优化 fetchBannerData 方法，添加缓存
fetchBannerData() {
  // 检查缓存
  const cachedBanner = wx.getStorageSync('banner_cache');
  const cacheTime = wx.getStorageSync('banner_cache_time');
  const now = Date.now();
  
  // 如果缓存存在且未过期（5分钟内），直接使用缓存
  if (cachedBanner && cacheTime && (now - cacheTime < 5 * 60 * 1000)) {
    this.setData({
      banner: cachedBanner,
      currentItem: cachedBanner[0] || null
    });
    return;
  }
  
  // 原有的API请求逻辑...
  util.request(api.GoodsList, {
    pageNum: 1,
    pageSize: 6, // 减少轮播图数量
    order: 'desc',
    sort: 'add_time'
  }).then(function (res) {
    if (res.success && res.data && res.data.goodsList) {
      const bannerData = res.data.goodsList.map(goods => ({
        id: goods.id,
        link: `/pages/goods/goods?id=${goods.id}`,
        linkType: 'page',
        imageUrl: goods.listPicUrl || goods.picUrl,
        name: goods.name,
        price: goods.retailPrice,
        brief: goods.goodsBrief
      }));

      // 缓存数据
      wx.setStorageSync('banner_cache', bannerData);
      wx.setStorageSync('banner_cache_time', Date.now());

      that.setData({
        banner: bannerData,
        currentItem: bannerData[0] || null
      });
    }
  });
}
```

### 2. 轮播组件优化

#### 修改 `app/wjhx/components/custom-carousel/index.wxml`：

```xml
<!-- 只在需要时渲染侧边图片 -->
<view class="carousel-prev" wx:if="{{showSideImages && items.length > 1 && currentIndex > 0}}" catchtap="prev">
  <view class="side-image-container" wx:if="{{items[utils.getPrevIndex(currentIndex, items.length)]}}">
    <image
      src="{{format.formatImageUrl(items[utils.getPrevIndex(currentIndex, items.length)].imageUrl)}}"
      mode="aspectFill"
      class="side-image side-image-prev"
      lazy-load="{{true}}"
    ></image>
  </view>
</view>

<!-- 主轮播区域 - 只渲染当前和相邻的图片 -->
<view class="carousel-main">
  <block wx:for="{{items}}" wx:key="id" wx:for-index="index">
    <view
      wx:if="{{Math.abs(index - currentIndex) <= 1 || (circular && ((index === 0 && currentIndex === items.length - 1) || (index === items.length - 1 && currentIndex === 0)))}}"
      class="carousel-item {{utils.getItemClass(index, currentIndex, itemsStatus, isChanging)}}"
      data-index="{{index}}"
      bindtap="onItemTap"
    >
      <image
        src="{{format.formatImageUrl(item.imageUrl)}}"
        mode="aspectFill"
        class="carousel-image {{currentIndex === index ? 'current' : ''}}"
        lazy-load="{{true}}"
      ></image>
    </view>
  </block>
</view>
```

### 3. 商品列表优化

#### 修改 `app/wjhx/pages/allGoods/allGoods.js`：

```javascript
data: {
  // 减少每页加载数量
  pageSize: 10, // 从20改为10
  // 添加图片加载状态
  imageLoadStates: {}
},

// 添加图片加载错误处理
imageError: function(e) {
  const index = e.currentTarget.dataset.index;
  console.log('图片加载失败:', index);
  
  // 使用默认图片替换
  const goodsList = this.data.goodsList;
  if (goodsList[index]) {
    goodsList[index].listPicUrl = '/static/images/goods1.jpg';
    this.setData({
      goodsList: goodsList
    });
  }
}
```

### 4. 图片格式化优化

#### 修改 `app/wjhx/utils/format.wxs`：

```javascript
// 添加图片尺寸优化
function formatImageUrl(url, size) {
  if (!url || url === '') {
    return '/static/images/redeem-placeholder.png';
  }

  if (url.indexOf('http://') === 0 || url.indexOf('https://') === 0) {
    return url;
  }

  if (url.indexOf('/weshop-wjhx/uploads') === 0) {
    var host = 'https://www.sxwjsm.com';
    var fullUrl = host + url;
    
    // 根据size参数添加图片尺寸优化
    if (size === 'thumb') {
      // 缩略图尺寸
      fullUrl += '?imageView2/2/w/200/h/200/q/80';
    } else if (size === 'medium') {
      // 中等尺寸
      fullUrl += '?imageView2/2/w/400/h/400/q/85';
    }
    
    return fullUrl;
  }

  return url;
}
```

### 5. 全局图片缓存策略

#### 在 `app/wjhx/utils/util.js` 中添加：

```javascript
/**
 * 图片预加载和缓存
 */
function preloadImages(imageUrls, callback) {
  let loadedCount = 0;
  const totalCount = imageUrls.length;
  
  if (totalCount === 0) {
    callback && callback();
    return;
  }
  
  imageUrls.forEach(url => {
    wx.getImageInfo({
      src: url,
      success: () => {
        loadedCount++;
        if (loadedCount === totalCount) {
          callback && callback();
        }
      },
      fail: () => {
        loadedCount++;
        if (loadedCount === totalCount) {
          callback && callback();
        }
      }
    });
  });
}

/**
 * 清理图片缓存
 */
function clearImageCache() {
  wx.removeStorageSync('banner_cache');
  wx.removeStorageSync('banner_cache_time');
}
```

## 实施步骤

1. **立即优化**：
   - 修改首页轮播图缓存逻辑
   - 减少轮播图数量从10张到6张
   - 优化轮播组件只渲染必要的图片

2. **中期优化**：
   - 实施图片尺寸优化
   - 添加图片加载错误处理
   - 优化商品列表分页大小

3. **长期优化**：
   - 实施全局图片缓存策略
   - 添加图片预加载机制
   - 考虑使用CDN加速

## 预期效果

- 减少50%以上的不必要图片加载
- 提升页面跳转速度30%以上
- 降低用户流量消耗
- 改善用户体验