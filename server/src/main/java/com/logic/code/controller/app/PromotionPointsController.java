package com.logic.code.controller.app;

import com.logic.code.entity.User;
import com.logic.code.service.PromotionPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 推广积分控制器
 */
@RestController
@RequestMapping("/wechat/promotion-points")
public class PromotionPointsController {
    
    @Autowired
    private PromotionPointsService promotionPointsService;
    
    /**
     * 获取推广积分记录
     */
    @GetMapping("/records")
    public Object getPromotionPointsRecords( User user,
                                          @RequestParam(defaultValue = "1") Integer page,
                                          @RequestParam(defaultValue = "20") Integer size) {
        if (user == null) {
            return ResponseUtil.unlogin();
        }
        
        return promotionPointsService.getPromotionPointsRecords(user.getId(), page, size);
    }
    
    /**
     * 获取推广积分统计
     */
    @GetMapping("/stats")
    public Object getPromotionPointsStats(@LoginUser User user) {
        if (user == null) {
            return ResponseUtil.unlogin();
        }
        
        return promotionPointsService.getPromotionPointsStats(user.getId());
    }
    
    /**
     * 获取推广者总积分
     */
    @GetMapping("/total")
    public Object getPromoterTotalPoints(@LoginUser User user) {
        if (user == null) {
            return ResponseUtil.unlogin();
        }
        
        Integer totalPoints = promotionPointsService.getPromoterTotalPoints(user.getId());
        return ResponseUtil.ok(totalPoints);
    }
}