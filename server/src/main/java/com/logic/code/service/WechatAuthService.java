package com.logic.code.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.alibaba.fastjson.JSONObject;
import com.logic.code.common.constants.WechatConstants;
import com.logic.code.common.utils.JsonUtils;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.common.utils.WebUtil;
import com.logic.code.config.WxMaConfiguration;
import com.logic.code.entity.User;
import com.logic.code.model.vo.LoginAuthParamVO;
import com.logic.code.model.vo.LoginAuthResultVO;
import com.logic.code.model.vo.WechatPhoneParamVO;
import com.logic.code.model.vo.WechatPhoneResultVO;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/5/7 14:28
 * @desc
 */
@Service
@Slf4j
public class WechatAuthService {

    @Autowired
    UserService userService;

    public LoginAuthResultVO login(LoginAuthParamVO loginAuthParamVO) throws WxErrorException {
        log.info("【微信登录】:{}", JSONObject.toJSONString(loginAuthParamVO));
        final WxMaService wxService = WxMaConfiguration.getMaService("wxca19c2771bc2ea41");
        WxMaJscode2SessionResult sessionInfo = wxService.getUserService().getSessionInfo(loginAuthParamVO.getCode());
        LoginAuthParamVO.UserInfoX.UserInfo userInfo = loginAuthParamVO.getUserInfo().getUserInfo();
        //根据openId查询用户是否已经注册
        //User user = userService.queryOneByCriteria(Criteria.of(User.class).andEqualTo(User::getWechatOpenId, openId));
        User user = userService.queryOneByCriteria(Criteria.of(User.class).andEqualTo(User::getWechatOpenId, sessionInfo.getOpenid()));
        if (user == null) {
            //注册新用户
            user = new User();
            user.setPassword("");
            user.setRegisterTime(new Date());
            user.setRegisterIp(WebUtil.getInstance().getIpAddress());
            user.setMobile("");
            user.setWechatOpenId(sessionInfo.getOpenid());
            user.setGender(userInfo.getGender());
            //user.setGender(EnumUtils.getEnum(GenderEnum.class, userInfo.getGender()));
            user.setNickname(userInfo.getNickName());
            user.setUsername(userInfo.getNickName()); // 同时设置 username 字段

            // 设置用户头像 - 从微信用户信息中获取
            if (userInfo.getAvatarUrl() != null && !userInfo.getAvatarUrl().isEmpty()) {
                user.setAvatar(userInfo.getAvatarUrl());
            }

            // 处理推广参数
            String promotionScene = loginAuthParamVO.getPromotionScene();
            if (promotionScene != null && !promotionScene.isEmpty()) {
                handlePromotionForNewUser(user, promotionScene);
            }

            userService.create(user);

            // 创建用户后生成推广码
            user.setPromotionCode("promo_" + user.getId());
            userService.updateById(user);
        }
        //查询用户信息
        User newUser = userService.queryOneByCriteria(Criteria.of(User.class).andEqualTo(User::getWechatOpenId, sessionInfo.getOpenid()));
        newUser.setLastLoginTime(new Date());
        newUser.setLastLoginIp(WebUtil.getInstance().getIpAddress());

        // 更新用户头像 - 每次登录时都检查并更新头像信息
        if (userInfo.getAvatarUrl() != null && !userInfo.getAvatarUrl().isEmpty()) {
            // 如果用户当前没有头像，或者微信头像与当前头像不同，则更新
            if (newUser.getAvatar() == null || newUser.getAvatar().isEmpty() ||
                !userInfo.getAvatarUrl().equals(newUser.getAvatar())) {
                newUser.setAvatar(userInfo.getAvatarUrl());
            }
        }

        // 更新用户昵称和用户名 - 每次登录时都检查并更新
        if (userInfo.getNickName() != null && !userInfo.getNickName().isEmpty()) {
            // 如果用户当前没有昵称，或者微信昵称与当前昵称不同，则更新
            if (newUser.getNickname() == null || newUser.getNickname().isEmpty() ||
                !userInfo.getNickName().equals(newUser.getNickname())) {
                newUser.setNickname(userInfo.getNickName());
                newUser.setUsername(userInfo.getNickName()); // 同时更新 username
            }
        }

        userService.updateNotNull(newUser);
        //生成token
        String token = JwtHelper.createJWT("wechat", JsonUtils.toJson(newUser), WechatConstants.JWT_TTL);
 /*       Claims claims = null;
        try {
            claims = JwtHelper.parseJWT(token);
            JwtHelper.setCurrentClaims(claims);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }*/

        return new LoginAuthResultVO(token, newUser);
    }

    /**
     * 获取微信绑定的手机号
     * @param phoneParamVO 包含获取手机号所需的参数
     * @return 手机号信息
     * @throws WxErrorException 微信异常
     */
    public WechatPhoneResultVO getPhoneNumber(WechatPhoneParamVO phoneParamVO) throws WxErrorException {
        final WxMaService wxService = WxMaConfiguration.getMaService("wxca19c2771bc2ea41");

        // 获取session
        WxMaJscode2SessionResult sessionInfo = wxService.getUserService().getSessionInfo(phoneParamVO.getCode());
        String openId = sessionInfo.getOpenid();

        // 解密手机号信息
        WxMaPhoneNumberInfo phoneNumberInfo = wxService.getUserService().getPhoneNoInfo(sessionInfo.getSessionKey(),
                phoneParamVO.getEncryptedData(), phoneParamVO.getIv());

        // 更新用户手机号
        User user = userService.queryOneByCriteria(Criteria.of(User.class).andEqualTo(User::getWechatOpenId, openId));
        if (user != null) {
            user.setMobile(phoneNumberInfo.getPhoneNumber());
            userService.updateNotNull(user);
        }

        // 构建返回结果
        WechatPhoneResultVO resultVO = new WechatPhoneResultVO();
        resultVO.setPhoneNumber(phoneNumberInfo.getPhoneNumber());
        resultVO.setCountryCode(phoneNumberInfo.getCountryCode());
        resultVO.setPurePhoneNumber(phoneNumberInfo.getPurePhoneNumber());

        return resultVO;
    }

    /**
     * 处理新用户的推广关系
     * @param newUser 新用户
     * @param promotionScene 推广场景值
     */
    private void handlePromotionForNewUser(User newUser, String promotionScene) {
        try {
            // 解析推广场景值
            Integer promoterId = parsePromotionScene(promotionScene);
            if (promoterId != null) {
                // 验证推广者是否存在
                User promoter = userService.queryOneByCriteria(
                    Criteria.of(User.class).andEqualTo(User::getId, promoterId)
                );

                if (promoter != null) {
                    // 设置推广关系
                    newUser.setPromoterId(promoterId);
                    newUser.setPromotionTime(new Date());
                    newUser.setPromotionLevel( 0); // 普通用户

                    // 更新推广者的推广统计
                    updatePromoterStats(promoter);

                    System.out.println("新用户 " + newUser.getNickname() + " 通过用户 " + promoter.getNickname() + " 的推广注册");
                }
            }
        } catch (Exception e) {
            System.err.println("处理推广关系失败: " + e.getMessage());
            // 推广关系处理失败不影响用户注册
        }
    }

    /**
     * 解析推广场景值，提取推广者ID
     * @param promotionScene 推广场景值，格式：promo_用户ID 或 直接的用户ID
     * @return 推广者用户ID
     */
    private Integer parsePromotionScene(String promotionScene) {
        try {
            if (promotionScene.startsWith("promo_")) {
                return Integer.parseInt(promotionScene.substring(6));
            } else {
                // 兼容直接传用户ID的情况
                return Integer.parseInt(promotionScene);
            }
        } catch (NumberFormatException e) {
            System.err.println("推广场景值格式错误: " + promotionScene);
            return null;
        }
    }

    /**
     * 更新推广者的统计信息
     * @param promoter 推广者
     */
    private void updatePromoterStats(User promoter) {
        try {
            // 查询推广者的推广用户数量
            long promotionCount = userService.countByCriteria(
                Criteria.of(User.class).andEqualTo(User::getPromoterId, promoter.getId())
            );

            // 更新推广统计
            promoter.setPromotionCount((int) promotionCount);

            // 如果是首次推广，记录首次推广时间
            if (promoter.getFirstPromotionTime() == null && promotionCount > 0) {
                promoter.setFirstPromotionTime(new Date());
                // 升级为推广员
                if (promoter.getPromotionLevel() == null || promoter.getPromotionLevel() < 1) {
                    promoter.setPromotionLevel((byte) 1);
                }
            }

            userService.updateById(promoter);

            System.out.println("更新推广者 " + promoter.getNickname() + " 的统计信息，当前推广用户数：" + promotionCount);
        } catch (Exception e) {
            System.err.println("更新推广者统计失败: " + e.getMessage());
        }
    }
}
