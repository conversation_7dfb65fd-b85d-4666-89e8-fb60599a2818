package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.entity.PromotionLevelConfig;
import com.logic.code.entity.PromotionPointsRecord;
import com.logic.code.entity.PointsRecord;
import com.logic.code.entity.User;
import com.logic.code.mapper.PromotionLevelConfigMapper;
import com.logic.code.mapper.PromotionPointsRecordMapper;
import com.logic.code.mapper.PointsRecordMapper;
import com.logic.code.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广等级服务类
 */
@Service
@Slf4j
public class PromotionLevelService {
    
    @Autowired
    private PromotionLevelConfigMapper promotionLevelConfigMapper;
    
    @Autowired
    private PromotionPointsRecordMapper promotionPointsRecordMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private PointsRecordMapper pointsRecordMapper;
    
    /**
     * 获取所有推广等级配置
     */
    public List<PromotionLevelConfig> getAllLevelConfigs() {
        return promotionLevelConfigMapper.getEnabledConfigs();
    }
    
    /**
     * 根据推广人数计算用户等级
     */
    public PromotionLevelConfig calculateUserLevel(Integer promotionCount) {
        if (promotionCount == null || promotionCount < 0) {
            promotionCount = 0;
        }
        
        PromotionLevelConfig config = promotionLevelConfigMapper.getConfigByPromotionCount(promotionCount);
        
        // 如果没有找到配置，返回默认的1级配置
        if (config == null) {
            config = promotionLevelConfigMapper.selectOne(
                new QueryWrapper<PromotionLevelConfig>()
                    .eq("level", 1)
                    .eq("is_enabled", true)
            );
        }
        
        return config;
    }
    
    /**
     * 更新用户推广等级
     */
    @Transactional
    public boolean updateUserPromotionLevel(Integer userId) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.error("用户不存在: {}", userId);
                return false;
            }
            
            // 计算当前推广人数
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("promoter_id", userId);
            Long promotionCount = userMapper.selectCount(wrapper);
            
            // 根据推广人数计算等级
            PromotionLevelConfig levelConfig = calculateUserLevel(promotionCount.intValue());
            if (levelConfig == null) {
                log.error("无法计算用户{}的推广等级，推广人数: {}", userId, promotionCount);
                return false;
            }
            
            // 更新用户等级和推广人数
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setPromotionLevel(levelConfig.getLevel());
            updateUser.setPromotionCount(promotionCount.intValue());
            
            // 如果是首次有推广用户，设置首次推广时间
            if (promotionCount > 0 && user.getFirstPromotionTime() == null) {
                updateUser.setFirstPromotionTime(new Date());
            }
            
            int result = userMapper.updateById(updateUser);
            
            if (result > 0) {
                log.info("用户{}推广等级更新成功: 等级={}, 推广人数={}", 
                    userId, levelConfig.getLevel(), promotionCount);
                return true;
            } else {
                log.error("用户{}推广等级更新失败", userId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新用户{}推广等级失败", userId, e);
            return false;
        }
    }
    
    /**
     * 处理推广关系建立，奖励推广积分
     */
    @Transactional
    public boolean handlePromotionReward(Integer promoterId, Integer promotedUserId) {
        try {
            log.info("开始处理推广奖励: 推广者={}, 被推广者={}", promoterId, promotedUserId);
            
            // 检查推广关系是否已存在
            PromotionPointsRecord existingRecord = promotionPointsRecordMapper.selectOne(
                new QueryWrapper<PromotionPointsRecord>()
                    .eq("promoter_id", promoterId)
                    .eq("promoted_user_id", promotedUserId)
            );
            
            if (existingRecord != null) {
                log.info("推广关系已存在，跳过奖励: 推广者={}, 被推广者={}", promoterId, promotedUserId);
                return true;
            }
            
            // 更新推广者等级
            updateUserPromotionLevel(promoterId);
            
            // 获取推广者当前等级配置
            User promoter = userMapper.selectById(promoterId);
            if (promoter == null) {
                log.error("推广者不存在: {}", promoterId);
                return false;
            }
            
            PromotionLevelConfig levelConfig = calculateUserLevel(promoter.getPromotionCount());
            if (levelConfig == null) {
                log.error("无法获取推广者{}的等级配置", promoterId);
                return false;
            }
            
            // 创建推广积分记录
            PromotionPointsRecord promotionRecord = new PromotionPointsRecord()
                .setPromoterId(promoterId)
                .setPromotedUserId(promotedUserId)
                .setPromotionLevel(levelConfig.getLevel())
                .setRewardPoints(levelConfig.getRewardPoints())
                .setPromotionTime(new Date())
                .setCreateTime(new Date());
            
            promotionPointsRecordMapper.insert(promotionRecord);
            
            // 创建积分记录
            PointsRecord pointsRecord = new PointsRecord()
                .setUserId(promoterId)
                .setType("earn")
                .setPoints(levelConfig.getRewardPoints())
                .setSource("promotion")
                .setSourceId(promotedUserId)
                .setDescription(String.format("推广用户获得积分（%s）", levelConfig.getLevelName()))
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
            
            pointsRecordMapper.insert(pointsRecord);
            
            // 更新推广者积分
            User updatePromoter = new User();
            updatePromoter.setId(promoterId);
            updatePromoter.setPoints((promoter.getPoints() != null ? promoter.getPoints() : 0) + levelConfig.getRewardPoints());
            //TODO
            //updatePromoter.setTotalPromotionPoints((promoter.getTotalPromotionPoints() != null ? promoter.getTotalPromotionPoints() : 0) + levelConfig.getRewardPoints());
            userMapper.updateById(updatePromoter);
            
            log.info("推广奖励处理成功: 推广者={}, 被推广者={}, 等级={}, 奖励积分={}", 
                promoterId, promotedUserId, levelConfig.getLevel(), levelConfig.getRewardPoints());
            
            return true;
            
        } catch (Exception e) {
            log.error("处理推广奖励失败: 推广者={}, 被推广者={}", promoterId, promotedUserId, e);
            return false;
        }
    }
    
    /**
     * 获取用户推广等级信息
     */
    public Map<String, Object> getUserPromotionLevelInfo(Integer userId) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 更新用户等级（确保数据最新）
            updateUserPromotionLevel(userId);
            
            // 重新获取用户信息
            user = userMapper.selectById(userId);
            
            // 获取当前等级配置
            PromotionLevelConfig currentLevelConfig = calculateUserLevel(user.getPromotionCount());
            
            // 获取下一等级配置
            PromotionLevelConfig nextLevelConfig = null;
            if (currentLevelConfig != null && currentLevelConfig.getLevel() < 5) {
                nextLevelConfig = promotionLevelConfigMapper.selectOne(
                    new QueryWrapper<PromotionLevelConfig>()
                        .eq("level", currentLevelConfig.getLevel() + 1)
                        .eq("is_enabled", true)
                );
            }
            
            // 获取推广积分总数
            Integer totalPromotionPoints = promotionPointsRecordMapper.getPromoterTotalPoints(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("promotionCount", user.getPromotionCount());
            result.put("promotionLevel", user.getPromotionLevel());
            result.put("totalPromotionPoints", totalPromotionPoints != null ? totalPromotionPoints : 0);
            result.put("firstPromotionTime", user.getFirstPromotionTime());
            result.put("promotionCode", user.getPromotionCode());
            
            if (currentLevelConfig != null) {
                result.put("currentLevelName", currentLevelConfig.getLevelName());
                result.put("currentLevelDescription", currentLevelConfig.getLevelDescription());
                result.put("currentRewardPoints", currentLevelConfig.getRewardPoints());
            }
            
            if (nextLevelConfig != null) {
                result.put("nextLevelName", nextLevelConfig.getLevelName());
                result.put("nextLevelDescription", nextLevelConfig.getLevelDescription());
                result.put("nextRewardPoints", nextLevelConfig.getRewardPoints());
                result.put("needPromotionCount", nextLevelConfig.getMinPromotionCount() - user.getPromotionCount());
            } else {
                result.put("isMaxLevel", true);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取用户{}推广等级信息失败", userId, e);
            throw new RuntimeException("获取推广等级信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取推广积分记录
     */
    public Map<String, Object> getPromotionPointsRecords(Integer userId, Integer page, Integer size) {
        if (page == null || page < 1) page = 1;
        if (size == null || size < 1) size = 20;
        
        Integer offset = (page - 1) * size;
        List<PromotionPointsRecord> records = promotionPointsRecordMapper.getPromoterRecords(userId, offset, size);
        Integer total = promotionPointsRecordMapper.getPromoterRecordsCount(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", records);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return result;
    }
    
    /**
     * 批量更新所有用户的推广等级（管理员功能）
     */
    @Transactional
    public void batchUpdateAllUserPromotionLevels() {
        try {
            log.info("开始批量更新所有用户推广等级...");
            
            // 获取所有有推广用户的用户
            List<User> promoters = userMapper.selectList(
                new QueryWrapper<User>()
                    .exists("SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id")
            );
            
            int successCount = 0;
            int failCount = 0;
            
            for (User promoter : promoters) {
                if (updateUserPromotionLevel(promoter.getId())) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
            
            log.info("批量更新推广等级完成: 成功={}, 失败={}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("批量更新推广等级失败", e);
            throw new RuntimeException("批量更新推广等级失败: " + e.getMessage());
        }
    }
}