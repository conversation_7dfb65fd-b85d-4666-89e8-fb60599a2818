# 日期格式化优化测试指南

## 优化内容

已为项目添加了日期格式化功能，将 ISO 格式的日期（如 `2025-07-27T02:31:34.000+00:00`）转换为易读的格式。

## 新增的格式化函数

在 `app/wjhx/utils/format.wxs` 中添加了两个新函数：

### 1. formatDateTime(dateTimeStr)
- 功能：将 ISO 格式日期转换为 `YYYY-MM-DD HH:mm` 格式
- 示例：`2025-07-27T02:31:34.000+00:00` → `2025-07-27 10:31`

### 2. formatRelativeTime(dateTimeStr)  
- 功能：显示相对时间（刚刚、5分钟前、1小时前、昨天等）
- 适用于需要显示相对时间的场景

## 已优化的页面

### 1. 积分页面 (`app/wjhx/pages/ucenter/points/points.wxml`)
- 位置：积分记录的时间显示
- 修改：`{{item.createTime}}` → `{{format.formatDateTime(item.createTime)}}`

### 2. 订单详情页面 (`app/wjhx/pages/ucenter/orderDetail/orderDetail.wxml`)
- 位置：下单时间显示
- 修改：`{{orderInfo.createTime}}` → `{{format.formatDateTime(orderInfo.createTime)}}`

### 3. 管理员订单页面 (`app/wjhx/pages/ucenter/admin/orders/orders.wxml`)
- 位置：订单创建时间显示
- 修改：`{{item.createTime}}` → `{{format.formatDateTime(item.createTime)}}`

### 4. 管理员用户页面 (`app/wjhx/pages/ucenter/admin/users/users.wxml`)
- 位置：用户注册时间显示（两处）
- 修改：`{{item.registerTime}}` 和 `{{userDetail.registerTime}}` → `{{format.formatDateTime(...)}}`

## 测试步骤

### 1. 测试积分页面
1. 进入小程序
2. 导航到 "我的" → "我的积分"
3. 查看积分记录中的时间显示是否为 `YYYY-MM-DD HH:mm` 格式

### 2. 测试订单详情页面
1. 进入 "我的" → "我的订单"
2. 点击任意订单查看详情
3. 确认下单时间显示格式正确

### 3. 测试管理员功能（需要管理员权限）
1. 进入管理员中心
2. 查看订单管理页面的订单时间
3. 查看用户管理页面的注册时间

## 预期效果

- 原始格式：`2025-07-27T02:31:34.000+00:00`
- 优化后格式：`2025-07-27 10:31`

## 注意事项

1. 时间会自动转换为本地时区
2. 如果日期解析失败，会显示原始字符串
3. 格式化函数已在各页面正确引入
4. 推广详情页面已有自己的时间格式化逻辑，暂未修改

## 扩展使用

如需在其他页面使用日期格式化：

1. 在 wxml 文件顶部添加：
```xml
<wxs module="format" src="../../utils/format.wxs"></wxs>
```

2. 在需要格式化的地方使用：
```xml
{{format.formatDateTime(item.createTime)}}
<!-- 或 -->
{{format.formatRelativeTime(item.createTime)}}
```