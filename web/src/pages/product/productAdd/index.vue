<template>
  <div class="" id="shopp-manager" v-loading="spinShow">
    <pages-header
      ref="pageHeader"
      :title="$route.params.id ? '编辑商品' : '添加商品'"
      :backUrl="$routeProStr + '/product/product_list'"
    ></pages-header>
    <el-card :bordered="false" shadow="never" class="mt16" :body-style="{ padding: '0px 20px' }">
      <el-tabs v-model="currentTab">
        <el-tab-pane v-for="(item, index) in headTab" :key="index" :label="item.tit" :name="item.name"></el-tab-pane>
      </el-tabs>
      <el-form
        class="formValidate mt20"
        ref="formValidate"
        :rules="ruleValidate"
        :model="formValidate"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.native.prevent
      >
        <!-- 基础信息-->
        <basic-info
          v-show="currentTab === '1'"
          :isCai="type"
          :formValidate="formValidate"
          :goodsType="goodsType"
          :treeSelect="treeSelect"
          :tileLabelList="tileLabelList"
          :progress="progress"
          :upload="upload"
          :videoIng="videoIng"
          @virtualbtn="virtualbtn"
          @handleDragStart="handleDragStart"
          @handleDragOver="handleDragOver"
          @handleDragEnter="handleDragEnter"
          @handleDragEnd="handleDragEnd"
          @handleRemove="handleRemove"
          @modalPicTap="modalPicTap"
          @addVideo="addVideo"
          @delVideo="delVideo"
          @addCate="addCate"
          @addGoodsTag="addGoodsTag"
        ></basic-info>

        <!-- 规格库存-->
        <spec-stock
          ref="specStock"
          v-show="currentTab === '2'"
          :formValidate="formValidate"
          :ruleList="ruleList"
          :attrs="attrs"
          :manyFormValidate="manyFormValidate"
          :oneFormValidate="oneFormValidate"
          :tableKey="tableKey"
          :oneFormBatch="oneFormBatch"
          :formDynamic="formDynamic"
          :canSel="canSel"
          :currentTemplateName="currentTemplateName"
          :isEdit="!!$route.params.id && $route.params.id !== '0'"
          @changeSpec="changeSpec"
          @confirm="confirm"
          @onMoveSpec="onMoveSpec"
          @changeCurrentIndex="changeCurrentIndex"
          @handleRemoveRole="handleRemoveRole"
          @attrChangeValue="attrChangeValue"
          @handleFocus="handleFocus"
          @addPic="addPic"
          @handleRemove2="handleRemove2"
          @attrDetailChangeValue="attrDetailChangeValue"
          @handleBlur="handleBlur"
          @handleSelImg="handleSelImg"
          @handleRemoveImg="handleRemoveImg"
          @handleShowPop="handleShowPop"
          @createAttr="createAttr"
          @handleAddRole="handleAddRole"
          @handleSaveAsTemplate="handleSaveAsTemplate"
          @batchAdd="batchAdd"
          @batchDel="batchDel"
          @modalPicTap="modalPicTap"
          @changeDefaultSelect="changeDefaultSelect"
          @changeDefaultShow="changeDefaultShow"
          @addGoodsCoupon="addGoodsCoupon"
          @see="see"
          @addVirtual="addVirtual"
        ></spec-stock>

        <!-- 商品详情-->
        <product-detail
          v-show="currentTab === '3'"
          :contents="contents"
          :content="content"
          @getEditorContent="getEditorContent"
        ></product-detail>

        <!-- 物流设置-->
        <logistics-setting
          v-show="headTab.length === 5 ? currentTab === '4' : false"
          :formValidate="formValidate"
          :templateList="templateList"
          @logisticsBtn="logisticsBtn"
          @addTemp="addTemp"
        ></logistics-setting>

        <!-- 会员价/佣金 -->
        <price-commission
          v-show="false"
          :formValidate="formValidate"
          :oneFormValidate="oneFormValidate"
          :manyFormValidate="manyFormValidate"
          :columnsInstall="columnsInstall"
          :columnsInstal2="columnsInstal2"
          :manyBrokerage="manyBrokerage"
          :manyBrokerageTwo="manyBrokerageTwo"
          :manyVipPrice="manyVipPrice"
          :manyVipDiscount="manyVipDiscount"
          @checkAllGroupChange="checkAllGroupChange"
          @changeVipPrice="changeVipPrice"
          @changeDiscount="changeDiscount"
          @brokerageSetUp="brokerageSetUp"
        ></price-commission>

        <!-- 营销设置-->
        <marketing-setting
          v-show="false"
          :formValidate="formValidate"
          :couponName="couponName"
          :dataLabel="dataLabel"
          :activity="activity"
          @handleClose="handleClose"
          @addCoupon="addCoupon"
          @openLabel="openLabel"
          @closeLabel="closeLabel"
          @addLabel="addLabel"
          @onchangeTime="onchangeTime"
          @handleRemoveRecommend="handleRemoveRecommend"
          @changeGoods="changeGoods"
        ></marketing-setting>

        <!-- 其他设置-->
        <other-setting
          v-show="headTab.length === 5 ? currentTab === '5' : currentTab === '4'"
          :formValidate="formValidate"
          :customBtn.sync="customBtn"
          :paramsType="paramsType"
          :paramsTypeList="paramsTypeList"
          :protectionList="protectionList"
          :CustomList="CustomList"
          @modalPicTap="modalPicTap"
          @changeParamsType="changeParamsType"
          @deleteRow="deleteRow"
          @handleAddParams="handleAddParams"
          @addProtection="addProtection"
          @customMessBtn="customMessBtn"
          @delcustom="delcustom"
          @addcustom="addcustom"
          @updateStoreInfo="updateStoreInfo"
        ></other-setting>

        <el-form-item>
          <el-button v-if="currentTab !== '1'" v-db-click @click="upTab">上一步</el-button>
          <el-button
            class="submission"
            v-if="currentTab !== '5' && formValidate.virtual_type == 0"
            v-db-click
            @click="downTab"
            >下一步</el-button
          >
          <el-button
            class="submission"
            v-if="currentTab !== '4' && formValidate.virtual_type != 0"
            v-db-click
            @click="downTab"
            >下一步</el-button
          >
          <el-button
            type="primary"
            class="submission"
            v-db-click
            @click="handleSubmit('formValidate')"
            v-if="$route.params.id || currentTab !== '1'"
            >保存</el-button
          >
        </el-form-item>
      </el-form>
      <el-dialog :visible.sync="modalPic" width="950px" scrollable title="上传商品图" :close-on-click-modal="false">
        <uploadPictures
          :isChoice="isChoice"
          @getPic="getPic"
          @getPicD="getPicD"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          v-if="modalPic"
        ></uploadPictures>
      </el-dialog>
      <el-dialog
        :visible.sync="addVirtualModel"
        width="720px"
        title="添加卡密"
        :show-close="true"
        :close-on-click-modal="false"
        @closed="initVirtualData"
      >
        <div class="trip"></div>
        <div class="type-radio">
          <el-form label-width="85px">
            <el-form-item label="卡密类型：">
              <el-radio-group v-model="disk_type" size="large">
                <el-radio :label="1">固定卡密</el-radio>
                <el-radio :label="2">一次性卡密</el-radio>
              </el-radio-group>
              <div v-if="disk_type == 1">
                <div class="stock-disk">
                  <el-input v-model="disk_info" size="large" type="textarea" :rows="4" placeholder="填写卡密信息" />
                </div>
                <div class="stock-input">
                  <!-- <el-input type="number" v-model="stock" size="large" :min='0' placeholder="填写库存数量">
                    <span slot="append">件</span>
                  </el-input> -->
                  <el-input-number :controls="false" :max="100000" :min="1" :step="1" :precision="0" v-model="stock" />
                  <span class="pl10">件</span>
                </div>
              </div>
              <div class="scroll-virtual" v-if="disk_type == 2">
                <div class="virtual-data mb10" v-for="(item, index) in virtualList" :key="index">
                  <span class="mr10 virtual-title">卡号{{ index + 1 }}：</span>
                  <el-input
                    class="mr10"
                    type="text"
                    v-model.trim="item.key"
                    style="width: 150px"
                    placeholder="请输入卡号(非必填)"
                  ></el-input>
                  <span class="mr10 virtual-title">卡密{{ index + 1 }}：</span>
                  <el-input
                    class="mr10"
                    type="text"
                    v-model.trim="item.value"
                    style="width: 150px"
                    placeholder="请输入卡密"
                  ></el-input>
                  <span class="deteal-btn" v-db-click @click="removeVirtual(index)">删除</span>
                </div>
              </div>
              <div class="add-more" v-if="disk_type == 2">
                <el-button class="h-33" type="primary" v-db-click @click="handleAdd">新增</el-button>
                <el-upload
                  class="ml10"
                  :action="cardUrl"
                  :data="uploadData"
                  :headers="header"
                  :on-success="upFile"
                  :before-upload="beforeUpload"
                >
                  <el-button>导入卡密</el-button>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button v-db-click @click="closeVirtual">取 消</el-button>
          <el-button type="primary" v-db-click @click="upVirtual">确 定</el-button>
        </span>
      </el-dialog>
    </el-card>
    <freightTemplate
      :template="template"
      v-on:changeTemplate="changeTemplate"
      @addSuccess="productGetTemplate"
      ref="templates"
    ></freightTemplate>
    <add-attr ref="addattr" @getList="userSearchs"></add-attr>
    <coupon-list
      ref="couponTemplates"
      @nameId="nameId"
      :couponids="formValidate.coupon_ids"
      :updateIds="updateIds"
      :updateName="updateName"
    ></coupon-list>
    <coupon-list ref="goodsCoupon" many="one" :luckDraw="true" @getCouponId="goodsCouponId"></coupon-list>
    <!-- 生成淘宝京东表单-->
    <el-dialog
      :visible.sync="modals"
      @closed="cancel"
      class="Box"
      title="复制淘宝、天猫、京东、苏宁、1688"
      :close-on-click-modal="false"
      width="720px"
    >
      <tao-bao ref="taobaos" v-if="modals" @on-close="onClose"></tao-bao>
    </el-dialog>
    <el-dialog :visible.sync="goods_modals" title="商品列表" footerHide class="paymentFooter" scrollable width="1000px">
      <goods-list v-if="goods_modals" ref="goodslist" :ischeckbox="true" @getProductId="getProductId"></goods-list>
    </el-dialog>
    <!-- 用户标签 -->
    <el-dialog
      :visible.sync="labelShow"
      title="请选择用户标签"
      :show-close="true"
      width="540px"
      :close-on-click-modal="false"
    >
      <userLabel ref="userLabel" @activeData="activeData" @close="labelClose"></userLabel>
    </el-dialog>
    <!-- 商品标签 -->
    <el-dialog
      :visible.sync="tagShow"
      title="请选择商品标签"
      :show-close="true"
      width="540px"
      :close-on-click-modal="false"
    >
      <goodsLabel
        ref="goodsLabel"
        :defaultLabelList="labelList"
        @activeLabel="activeLabel"
        @close="labelClose"
      ></goodsLabel>
    </el-dialog>
  </div>
</template>

<script>
import userLabel from '@/components/labelList';
import useLabel from '@/components/goodsLabel/useLabel';
import goodsLabel from '@/components/goodsLabel';
import { mapState } from 'vuex';
import uploadPictures from '@/components/uploadPictures';
import freightTemplate from '@/components/freightTemplate';
import couponList from '@/components/couponList';
import addAttr from '../productAttr/addAttr';
import goodsList from '@/components/goodsList/index';
import taoBao from './taoBao';
import { userLabelAddApi } from '@/api/user';
import {
  productInfoApi,
  cascaderListApi,
  productAddApi,
  generateAttrApi,
  productGetRuleApi,
  productGetTemplateApi,
  productGetTempKeysApi,
  checkActivityApi,
  productCache,
  cacheDelete,
  uploadType,
  importCard,
  productCreateApi,
  getProductTypeConfig,
  ruleAddApi,
  paramListApi,
  paramCategoriesApi,
  paramInfoApi,
  productProtectionListApi,
  productLabelUseListApi,
} from '@/api/product';
import Setting from '@/setting';
import { getCookies } from '@/libs/util';
import { uploadByPieces } from '@/utils/upload'; //引入uploadByPieces方法
import { isFileUpload, isVideoUpload, arraysEqual } from '@/utils';
import checkArray from '@/libs/permission';
import {
  GoodsTableHead,
  VirtualTableHead,
  VirtualTableHead2,
  columns2,
  columns3,
  CustomList,
  RuleValidate,
} from './defaultData.js';
import BasicInfo from './components/BasicInfo.vue';
import SpecStock from './components/SpecStock.vue';
import ProductDetail from './components/ProductDetail.vue';
import LogisticsSetting from './components/LogisticsSetting.vue';
import PriceCommission from './components/PriceCommission.vue';
import MarketingSetting from './components/MarketingSetting.vue';
import OtherSetting from './components/OtherSetting.vue';
import { formatRichText } from '@/utils/editorImg';

export default {
  name: 'ProductAdd',
  components: {
    uploadPictures,
    freightTemplate,
    addAttr,
    couponList,
    taoBao,
    goodsList,
    userLabel,
    goodsLabel,
    useLabel,
    BasicInfo,
    SpecStock,
    ProductDetail,
    LogisticsSetting,
    PriceCommission,
    MarketingSetting,
    OtherSetting,
  },
  data() {
    return {
      labelShow: false,
      tagShow: false,
      dataLabel: [],
      headTab: [
        { tit: '基础信息', name: '1' },
        { tit: '规格库存', name: '2' },
        { tit: '商品详情', name: '3' },
        { tit: '物流设置', name: '4' },
        { tit: '其他设置', name: '5' },
      ],
      virtual: [
        { tit: '普通商品', id: 0, tit2: '物流发货' },
        { tit: '卡密/网盘', id: 1, tit2: '自动发货' },
        { tit: '优惠券', id: 2, tit2: '自动发货' },
        { tit: '虚拟商品', id: 3, tit2: '虚拟发货' },
      ],
      seletVideo: 0, //选择视频类型
      customBtn: 0, //自定义留言开关
      content: '',
      contents: '',
      fileUrl: Setting.apiBaseURL + '/file/upload',
      fileUrl2: Setting.apiBaseURL + '/file/video_upload',
      cardUrl: Setting.apiBaseURL + '/file/upload/1',
      upload_type: '', //视频上传类型 1 本地上传 2 3 4 OSS上传
      uploadData: {}, // 上传参数
      header: {},
      type: 0,
      modals: false,
      goods_modals: false,
      spinShow: false,
      openSubimit: false,
      virtualList: [
        {
          key: '',
          value: '',
        },
      ],

      // 批量设置表格data
      oneFormBatch: [
        {
          pic: '',
          price: void 0,
          cost: void 0,
          ot_price: void 0,
          stock: void 0,
          bar_code: '',
          bar_code_number: '',
          weight: void 0,
          volume: void 0,
          virtual_list: [],
        },
      ],

      // 规格数据
      formDynamic: {
        attrsName: '',
        attrsVal: '',
      },
      currentTemplateName: '', // 当前选择的规格模板名称
      disk_type: 1, //卡密类型
      tabIndex: 0,
      tabName: '',
      formDynamicNameData: [],
      isBtn: false,
      columns2: columns2,
      columns3: columns3,
      columns: [],
      columnsInstall: [],
      columnsInstal2: [],
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12,
      },
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8,
      },
      //自定义留言下拉选择
      CustomList: CustomList,
      //自定义留言内容
      currentIndex: 0,

      formValidate: {
        disk_info: '', //卡密类型
        logistics: ['1'], //选择物流方式
        freight: 2, //运费设置
        postage: 0, //设置运费金额
        recommend: [], //商品推荐
        presale_day: 1, //预售发货时间-结束
        presale: false, //预售商品开关
        is_limit: false,
        limit_type: 0,
        limit_num: 0,
        vip_product: false, //付费会员专属开关
        custom_form: [], //自定义留言
        store_name: '',
        cate_id: [],
        label_id: [],
        keyword: '',
        unit_name: '',
        unit_price: 0, // 商品单价
        store_info: '',
        image: '',
        recommend_image: '',
        slider_image: [],
        description: '',
        ficti: 0,
        give_integral: 0,
        sort: 0,
        is_show: 1,
        is_gift: 0, // 开启送礼品
        gift_price: 0,
        is_hot: 0,
        is_benefit: 0,
        is_best: 0,
        is_new: 0,
        is_good: 0,
        is_postage: 0,
        is_sub: [],
        recommend_list: [],
        params_list: [], //商品参数
        display_systems: [], // 商品展示系统列表
        detail_tag: '', // 商品详情标签
        virtual_type: 0,
        id: 0,
        spec_type: 0,
        is_virtual: 0,
        video_link: '',
        temp_id: '',
        attrs: [],
        items: [
          {
            pic: '',
            price: 0,
            cost: 0,
            ot_price: 0,
            stock: 0,
            bar_code: '',
            bar_code_number: '',
          },
        ],
        activity: ['默认'],
        header: [],
        selectRule: '',
        label_list: [],
        protection_list: [],
      },
      ruleList: [],
      templateList: [],
      createBnt: true,
      showIput: false,
      manyFormValidate: [],
      // 单规格表格data
      oneFormValidate: [
        {
          pic: '',
          price: 0,
          cost: 0,
          ot_price: 0,
          stock: 0,
          bar_code: '',
          bar_code_number: '',
          weight: 0,
          volume: 0,
          virtual_list: [],
          coupon_id: 0,
        },
      ],
      images: [],
      imagesTable: '',
      currentTab: '1',
      isChoice: '',
      loading: false,
      modalPic: false,
      addVirtualModel: false,
      template: false,
      uploadList: [],
      treeSelect: [],
      picTit: '',
      tableIndex: 0,
      ruleValidate: RuleValidate,
      upload: {
        videoIng: false, // 是否显示进度条；
      },
      videoIng: false, // 是否显示进度条；
      progress: 0, // 进度条默认0
      stock: 0,
      disk_info: '',
      videoLink: '',
      attrs: [],
      activity: { 默认: 'red', 秒杀: 'blue', 砍价: 'green', 拼团: 'yellow' },
      couponName: [],
      updateIds: [],
      updateName: [],
      couponIds: '',
      couponNames: [],
      columnsInstalM: [],
      moveIndex: '',
      addValue: '',
      visible: false,
      typeConfig: [],
      goodsType: [],
      paramsTypeList: [],
      paramsType: null,
      canSel: true, // 规格图片添加判断
      changeAttrValue: '', //修改的规格值
      tableKey: 0,
      protectionList: [], // 服务保障
      labelList: [],
      tileLabelList: [],
      // 佣金相关数据
      manyBrokerage: undefined, // 一级返佣
      manyBrokerageTwo: undefined, // 二级返佣
      manyVipPrice: undefined, // 会员价
      manyVipDiscount: undefined, // 会员折扣
    };
  },
  computed: {
    ...mapState('media', ['isMobile']),
    labelWidth() {
      return this.isMobile ? undefined : '120px';
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    },
    labelBottom() {
      return this.isMobile ? undefined : '15px';
    },
  },
  watch: {
    typeConfig(val) {
      if (val.length) {
        // 对virtual中的id等于val中的id的
        this.goodsType = this.virtual.filter((item) => {
          return val.includes(item.id + '');
        });
      } else {
        this.goodsType = this.virtual;
      }
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.bus.$emit('onTagsViewRefreshRouterView', this.$route.path);
    next();
  },
  created() {
    this.getRuleList();
    this.getTemplateList();
    this.getCategoryList();
    //this.getProductTypeConfig();
    this.getLabelList();
    // If editing an existing product
    if (this.$route.params.id && this.$route.params.id !== '0') {
      this.getProductInfo();
    }
  },
  async mounted() {
    if (this.$route.params.id !== '0' && this.$route.params.id) {
      await this.getInfo();
    } else if (this.$route.params.id === '0') {
      this.getProductCache();
    } else {
      this.getproductLabelUseListApi();
    }
    if (this.$route.query.type) {
      this.modals = true;
      this.type = this.$route.query.type;
    } else {
      this.type = 0;
    }
    this.goodsCategory();
    this.productGetRule();
    this.productGetTemplate();
    this.paramsGetTemplate();
    this.uploadType();
    this.productConfig();
    this.watchActivity();
    this.getProtectionList();
  },
  methods: {
    getProductCache() {
      productCache()
        .then((res) => {
          let data = res.data.info;
          this.getproductLabelUseListApi();

          if (!Array.isArray(data)) {
            let cate_id = data.cate_id.map(Number);
            let label_id = data.label_id.map(Number);
            this.attrs = data.items || [];
            let ids = [];
            if (data.coupons) {
              data.coupons.map((item) => {
                ids.push(item.id);
              });
              this.couponName = data.coupons;
            }

            this.formValidate = data;
            this.dataLabel = data.label_id;
            this.formValidate.coupon_ids = ids;
            this.updateIds = ids;
            this.updateName = data.coupons;
            this.formValidate.cate_id = cate_id;
            this.oneFormValidate = data.attrs;
            this.generateHeader(this.attrs);
            this.formValidate.logistics = data.logistics || ['1'];
            this.formValidate.header = [];
            this.manyFormValidate = data.attrs;
            this.spec_type = data.spec_type;
            this.formValidate.is_virtual = data.is_virtual;
            this.formValidate.custom_form = data.custom_form || [];
            if (this.formValidate.custom_form.length != 0) {
              this.customBtn = 1;
            }
            this.attrs.map((item) => {
              if (item.add_pic) this.canSel = false;
            });
            this.virtualbtn(data.virtual_type, 1);
            if (data.spec_type === 0) {
              this.manyFormValidate = [];
            } else {
              this.createBnt = true;
              this.oneFormValidate = [
                {
                  pic: data.image,
                  price: 0,
                  cost: 0,
                  ot_price: 0,
                  stock: 0,
                  bar_code: '',
                  bar_code_number: '',
                  weight: 0,
                  volume: 0,
                  virtual_list: [],
                  coupon_id: 0,
                },
              ];
            }
            this.watchActivity();
            this.spinShow = false;
          }
        })
        .catch((err) => {
          this.$message.error(err.msg);
        });
    },
    getProtectionList() {
      /*productProtectionListApi({ page: 0, limit: 0, status: 1 }).then((res) => {
        this.protectionList = res.data.list;
      });*/
    },
    getproductLabelUseListApi() {
      productLabelUseListApi().then((res) => {
        // 合并数组中所有的list
        this.tileLabelList = res.data.flatMap((item) => item.list);
        let labelList = res.data;
        if (this.formValidate.label_list.length) {
          this.formValidate.label_list.map((el) => {
            labelList.map((re) => {
              re.list.map((label) => {
                if (label.id === el) {
                  label.active = true;
                } else {
                  label.active = false;
                }
              });
            });
          });
        } else {
          labelList.map((el) => {
            el.list.map((label) => {
              label.active = false;
            });
          });
        }
        this.labelList = labelList;
      });
    },
    addProtection() {
      this.$router.push({ path: this.$routeProStr + '/product/protection/list' });
    },
    productConfig() {
      /*getProductTypeConfig().then((res) => {
        this.typeConfig = res.data;
      });*/
    },
    beforeUpload(file) {
      return isFileUpload(file);
    },
    // 分片上传
    videoSaveToUrl(file) {
      if (isVideoUpload(file)) {
        uploadByPieces({
          file: file, // 视频实体
          pieceSize: 3, // 分片大小
          success: (data) => {
            this.formValidate.video_link = data.file_path;
            this.progress = 100;
          },
          error: (e) => {
            this.$message.error(e.msg);
          },
          uploading: (chunk, allChunk) => {
            this.videoIng = true;
            let st = Math.floor((chunk / allChunk) * 100);
            this.progress = st;
          },
        });
      }
      return false;
    },
    // 类型选择/填入内容判断
    virtualbtn(index, type) {
      if (type != 1) {
        if (this.$route.params.id) return this.$message.error('编辑商品不支持切换商品类型');
        this.formValidate.is_sub = [];
        let id = this.$route.params.id;
        if (id) {
          checkActivityApi(id)
            .then((res) => {})
            .catch((res) => {
              this.formValidate.spec_type = this.spec_type;
              this.$message.error(res.msg);
            });
        } else {
          if (this.formValidate.spec_type == 1) {
            this.generate(1);
          }
        }
      }
      // 定义基础商品和虚拟商品的标签页配置
      const baseHeadTabs = [
        { tit: '基础信息', name: '1' },
        { tit: '规格库存', name: '2' },
        { tit: '商品详情', name: '3' },
        { tit: '物流设置', name: '4' },
        { tit: '其他设置', name: '5' },
      ];
      const virtualHeadTabs = [
        { tit: '基础信息', name: '1' },
        { tit: '规格库存', name: '2' },
        { tit: '商品详情', name: '3' },
        { tit: '其他设置', name: '4' },
      ];

      switch (index) {
        case 0: // 普通商品
          this.formValidate.virtual_type = 0;
          this.formValidate.is_virtual = 0;
          this.headTab = baseHeadTabs;
          break;

        case 1: // 卡密/网盘商品
          this.formValidate.virtual_type = 1;
          this.formValidate.postage = 0;
          this.formValidate.is_virtual = 1;
          this.headTab = virtualHeadTabs;
          break;

        case 2: // 优惠券商品
          this.formValidate.virtual_type = 2;
          this.formValidate.is_virtual = 1;
          this.headTab = virtualHeadTabs;
          break;

        case 3: // 虚拟商品
          this.formValidate.virtual_type = 3;
          this.formValidate.is_virtual = 1;
          this.headTab = virtualHeadTabs;
          break;
      }
    },
    // 新增分类
    addCate() {
      this.$modalForm(productCreateApi()).then(() => this.goodsCategory());
    },
    // 物流方式选择
    logisticsBtn(e) {
      this.formValidate.logistics = e;
    },
    // 新增标签
    addLabel() {
      this.$modalForm(userLabelAddApi(0)).then(() => this.userLabel());
    },
    // 选择标签
    addGoodsTag() {
      this.tagShow = true;
    },
    // 自定义留言 开启关闭
    customMessBtn(e) {
      if (!e) {
        this.formValidate.custom_form = [];
      }
      this.customBtn = e;
    },
    // 自定义留言 新增表单
    addcustom() {
      if (this.formValidate.custom_form.length > 9) {
        this.$message.warning('最多添加10条');
      } else {
        this.formValidate.custom_form.push({
          title: '',
          label: 'text',
          value: '',
          status: false,
        });
      }
    },
    // 删除
    delcustom(index) {
      this.formValidate.custom_form.splice(index, 1);
    },
    // 更新商品简介富文本内容
    updateStoreInfo(content) {
      this.formValidate.store_info = content;
    },
    // 预售具体日期
    onchangeTime(e) {
      this.formValidate.presale_time = e;
    },
    // 商品详情
    getEditorContent(data) {
      this.content = data;
    },
    cancel() {
      this.modals = false;
    },
    // 上传头部token
    getToken() {
      this.header['Authori-zation'] = 'Bearer ' + getCookies('token');
    },
    // 导入卡密
    upFile(res) {
      importCard({ file: res.data.src }).then((res) => {
        this.virtualList = this.virtualList.concat(res.data);
      });
    },
    //获取视频上传类型
    uploadType() {
      /*uploadType().then((res) => {
        this.upload_type = res.data.upload_type;
      });*/
    },
    // 初始化数据展示
    infoData(data, isCopy) {
      let cate_id = data.cate_id.map(Number);
      let label_id = data.label_id.map(Number);
      this.attrs = data.items || [];
      let ids = [];
      data.coupons.map((item) => {
        ids.push(item.id);
      });
      this.formValidate = data;
      this.seletVideo = data.seletVideo;
      this.contents = data.description;
      this.couponName = data.coupons;
      this.formValidate.coupon_ids = ids;
      this.updateIds = ids;
      this.dataLabel = data.label_id;
      this.updateName = data.coupons;
      this.virtualbtn(data.virtual_type, 1);
      this.formValidate.logistics = data.logistics || ['1'];
      this.formValidate.custom_form = data.custom_form || [];
      if (this.formValidate.custom_form.length != 0) {
        this.customBtn = 1;
      }
      this.formValidate.cate_id = cate_id;
      if (data.attr) {
        this.oneFormValidate = [data.attr];
      }
      this.getproductLabelUseListApi();

      this.formValidate.header = [];
      this.spec_type = data.spec_type;
      this.formValidate.spec_type = this.spec_type;
      this.formValidate.is_virtual = data.is_virtual;
      this.attrs.map((item) => {
        if (item.add_pic) this.canSel = false;
      });
      if (data.spec_type === 0) {
        this.manyFormValidate = [];
      } else {
        this.createBnt = true;
        this.oneFormValidate = [
          {
            pic: '',
            price: 0,
            cost: 0,
            ot_price: 0,
            stock: 0,
            bar_code: '',
            bar_code_number: '',
            weight: 0,
            volume: 0,
            virtual_list: [],
            coupon_id: 0,
          },
        ];

        this.generateHeader(this.attrs);
        this.manyFormValidate = [...this.oneFormBatch, ...data.attrs];
      }

      setTimeout((e) => {
        this.checkAllGroup(data.is_sub);
      }, 1000);
      this.watchActivity();
    },
    //关闭淘宝弹窗并生成数据；
    onClose(data) {
      this.modals = false;
      this.infoData(data, 1);
    },

    checkMove(evt) {
      this.moveIndex = evt.draggedContext.index;
    },
    end() {
      this.moveIndex = '';
      this.generate(1);
    },
    // 单独设置会员设置
    checkAllGroupChange(data) {
      // Since we're removing member commission functionality, this method can be simplified
      this.formValidate.is_sub = [];
    },
    checkAllGroup(data) {
      // Since we're removing member commission functionality, this method can be simplified
      this.columnsInstall = this.columns2.slice(0, this.attrs.length + 3);
      this.columnsInstal2 = this.columnsInstalM.slice(0, this.attrs.length + 3);
    },
    // 添加优惠券
    addCoupon() {
      this.$refs.couponTemplates.isTemplate = true;
      this.$refs.couponTemplates.tableList();
    },
    // 规格中优惠券查看
    see(data, name, index) {
      this.tabName = name;
      this.tabIndex = index;

      if (this.formValidate.virtual_type === 1) {
        if (data.disk_info != '') {
          this.disk_type = 1;
          this.disk_info = data.disk_info;
          this.stock = data.stock;
        } else if (data.virtual_list.length) {
          this.disk_type = 2;
          this.virtualList = data.virtual_list;
        }
        this.addVirtualModel = true;
      } else {
        this.$refs.goodsCoupon.isTemplate = true;
        this.$refs.goodsCoupon.tableList(3);
      }
    },
    // 添加优惠券
    addGoodsCoupon(index, name) {
      this.tabIndex = index;
      this.tabName = name;
      this.$refs.goodsCoupon.isTemplate = true;
      this.$refs.goodsCoupon.tableList(3);
    },
    addVirtual(index, name) {
      this.tabIndex = index;
      this.tabName = name;
      this.addVirtualModel = true;
    },
    // 提交卡密信息
    upVirtual() {
      if (this.disk_type == 2) {
        for (let i = 0; i < this.virtualList.length; i++) {
          const element = this.virtualList[i];
          if (!element.value) {
            this.$message.error('请输入所有卡密');
            return;
          }
        }
        this.$set(this[this.tabName][this.tabIndex], 'virtual_list', this.virtualList);
        this.$set(this[this.tabName][this.tabIndex], 'stock', this.virtualList.length);
        this.virtualList = [
          {
            key: '',
            value: '',
          },
        ];
        this.$set(this[this.tabName][this.tabIndex], 'disk_info', '');
      } else {
        if (!this.disk_info.length) {
          return this.$message.error('请填写卡密信息');
        }
        if (!this.stock) {
          return this.$message.error('请填写库存数量');
        }
        this.$set(this[this.tabName][this.tabIndex], 'stock', Number(this.stock));
        this.$set(this[this.tabName][this.tabIndex], 'stock', Number(this.stock));
        this.$set(this[this.tabName][this.tabIndex], 'disk_info', this.disk_info);
        this.$set(this[this.tabName][this.tabIndex], 'virtual_list', []);
      }
      this.addVirtualModel = false;
      this.closeVirtual();
    },
    //  初始化卡密数据信息
    closeVirtual() {
      this.addVirtualModel = false;
      this.virtualList = [
        {
          key: '',
          value: '',
        },
      ];
      this.disk_info = '';
      this.stock = 0;
    },
    //对象数组去重；
    uniqueArray(arr) {
      const seen = {};
      return arr.filter((item) => {
        const key = JSON.stringify(item); // 使用 JSON.stringify 生成唯一键
        if (seen[key]) {
          return false;
        } else {
          seen[key] = true;
          return true;
        }
      });
    },
    // 获取优惠券id数据
    nameId(id, names) {
      this.formValidate.coupon_ids = id;
      this.couponName = this.uniqueArray(names);
    },
    // 获取优惠券信息
    goodsCouponId(data) {
      this.$set(this[this.tabName][this.tabIndex], 'coupon_id', data.id);
      this.$set(this[this.tabName][this.tabIndex], 'coupon_name', data.title);
      this.$refs.goodsCoupon.isTemplate = false;
    },
    handleClose(name) {
      const index = this.couponName.findIndex(item => item === name);
      if (index !== -1) {
        this.couponName.splice(index, 1);
      }
      let couponIds = this.formValidate.coupon_ids;
      couponIds.splice(index, 1);
      this.updateIds = couponIds;
      this.updateName = this.couponName;
    },
    // 添加运费模板
    addTemp() {
      this.$refs.templates.isTemplate = true;
    },
    addVideo() {
      this.$videoModal((e) => {
        this.formValidate.video_link = e;
      });
    },
    // 删除视频；
    delVideo() {
      this.$set(this.formValidate, 'video_link', '');
      this.$set(this, 'progress', 0);
      this.videoIng = false;
      this.upload.videoIng = false;
    },
    zh_uploadFile() {
      if (this.seletVideo == 1) {
        this.formValidate.video_link = this.videoLink;
      } else {
        this.$refs.refid.click();
      }
    },
    // 上传视频
    zh_uploadFile_change(evfile) {
      let suffix = evfile.target.files[0].name.substr(evfile.target.files[0].name.indexOf('.'));
      if (suffix.indexOf('.mp4') === -1) {
        return this.$message.error('只能上传MP4文件');
      }
      let types = {
        key: evfile.target.files[0].name,
        contentType: evfile.target.files[0].type,
      };
      productGetTempKeysApi(types)
        .then((res) => {
          this.$videoCloud
            .videoUpload({
              type: res.data.type,
              evfile: evfile,
              res: res,
              uploading(status, progress) {
                this.upload.videoIng = status;
                if (res.status == 200) {
                  this.progress = 100;
                }
              },
            })
            .then((res) => {
              this.formValidate.video_link = res.url;
              this.$message.success('视频上传成功');
              this.upload.videoIng = false;
            })
            .catch((res) => {
              this.$message.error(res);
            });
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 上一页；
    upTab() {
      const currentIndex = this.headTab.findIndex(item => item.name === this.currentTab);
      if (currentIndex > 0) {
        this.currentTab = this.headTab[currentIndex - 1].name;
      }
    },
    // 下一页；
    downTab() {
      const currentIndex = this.headTab.findIndex(item => item.name === this.currentTab);
      if (currentIndex < this.headTab.length - 1) {
        this.currentTab = this.headTab[currentIndex + 1].name;
      }
    },
    // 属性弹窗回调函数；
    userSearchs() {
      this.productGetRule();
    },
    // 添加规则；
    addRule() {
      this.$refs.addattr.modal = true;
    },
    // 批量设置分佣；
    brokerageSetUp() {
      // 批量设置佣金
      if (this.manyBrokerage !== undefined || this.manyBrokerageTwo !== undefined) {
        this.manyFormValidate.forEach((item, index) => {
          if (index > 0) { // 跳过批量设置行
            if (this.manyBrokerage !== undefined) {
              this.$set(item, 'brokerage', this.manyBrokerage);
            }
            if (this.manyBrokerageTwo !== undefined) {
              this.$set(item, 'brokerage_two', this.manyBrokerageTwo);
            }
          }
        });
        this.$message.success('批量设置成功');
      }

      // 批量设置会员价
      if (this.manyVipPrice !== undefined) {
        this.manyFormValidate.forEach((item, index) => {
          if (index > 0) { // 跳过批量设置行
            this.$set(item, 'vip_price', this.manyVipPrice);
          }
        });
      }

      // 批量设置会员折扣
      if (this.manyVipDiscount !== undefined) {
        this.manyFormValidate.forEach((item, index) => {
          if (index > 0) { // 跳过批量设置行
            const discountPrice = (item.price * this.manyVipDiscount / 100).toFixed(2);
            this.$set(item, 'vip_price', parseFloat(discountPrice));
          }
        });
      }
    },
    // 会员价变化
    changeVipPrice(index, type) {
      // 处理会员价变化
      console.log('changeVipPrice:', index, type);
    },
    // 会员折扣变化
    changeDiscount(index, type) {
      // 处理会员折扣变化
      console.log('changeDiscount:', index, type);
    },
    // 确保批量设置对象包含所有必要的属性
    ensureBatchDataProperties(batchData, attrs) {
      if (!batchData || !attrs) return batchData;

      attrs.forEach((item) => {
        if (item.value && !batchData.hasOwnProperty(item.value)) {
          this.$set(batchData, item.value, '');
        }
      });

      return batchData;
    },
    // 批量设置会员价
    vipPriceSetUp() {
      if (this.manyVipPrice <= 0) {
        return this.$message.error('请填写会员价在进行批量添加');
      } else {
        for (let val of this.manyFormValidate) {
          this.$set(val, 'vip_price', this.manyVipPrice);
        }
      }
    },
    // 新增卡密
    handleAdd() {
      this.virtualList.push({
        key: '',
        value: '',
      });
    },
    // 初始化卡密信息
    initVirtualData(status) {
      this.virtualList = [
        {
          key: '',
          value: '',
        },
      ];
    },
    removeVirtual(index) {
      this.virtualList.splice(index, 1);
    },
    // 清空批量规格信息
    batchDel() {
      this.oneFormBatch = [
        {
          pic: '',
          price: void 0,
          cost: void 0,
          ot_price: void 0,
          stock: void 0,
          bar_code: '',
          bar_code_number: '',
          weight: void 0,
          volume: void 0,
          virtual_list: [],
        },
      ];
    },
    confirm(name) {
      this.createBnt = true;
      this.formValidate.selectRule = name;
      this.currentTemplateName = name; // 更新当前模板名称
      this.attrs = [];
      if (this.formValidate.selectRule.trim().length <= 0) {
        return this.$message.error('请选择属性');
      }
      this.ruleList.forEach((item, index) => {
        if (item.rule_name === this.formValidate.selectRule) {
          this.attrs = [...item.rule_value];
        }
      });
      this.canSel = true;
      this.generateAttr(this.attrs);
    },
    // 选择规格模板
    handleCommand(e) {},
    // 获取商品属性模板；
    productGetRule() {
      productGetRuleApi().then((res) => {
        this.ruleList = res.data;
      });
    },
    // 获取运费模板；
    productGetTemplate() {
      productGetTemplateApi().then((res) => {
        this.templateList = res.data;
      });
    },
    paramsGetTemplate() {
      paramCategoriesApi().then((res) => {
        this.paramsTypeList = res.data;
      });
    },
    changeParamsType(e) {
      e ? this.getParams(e) : (this.formValidate.params_list = []);
    },
    // 根据已有参数设置参数类型
    setParamsTypeFromAttributes() {
      if (!this.formValidate.params_list || this.formValidate.params_list.length === 0) {
        return;
      }

      const firstAttributeId = this.formValidate.params_list[0].attributeId;
      if (!firstAttributeId) {
        return;
      }

      // 通过属性ID查找对应的分类
      paramListApi({ page: 1, limit: 1000 }).then((res) => {
        if (res.data && res.data.list) {
          const attribute = res.data.list.find(attr => attr.id === firstAttributeId);
          if (attribute && attribute.attributeCategoryId) {
            this.paramsType = attribute.attributeCategoryId;
          }
        }
      }).catch((err) => {
        console.warn('获取参数分类失败:', err);
      });
    },
    getParams(categoryId) {
      // 根据分类ID获取属性列表
      paramListApi({ categoryId: categoryId, page: 1, limit: 100 }).then((res) => {
        // 如果是编辑模式且已有参数数据，不要覆盖现有数据
        const isEditMode = this.$route.params.id && this.$route.params.id !== '0';
        const hasExistingParams = this.formValidate.params_list && this.formValidate.params_list.length > 0;

        if (isEditMode && hasExistingParams) {
          // 编辑模式下，保持现有参数数据不变
          return;
        }

        // 新建模式或无现有数据时，使用模板数据
        this.formValidate.params_list = res.data.list.map(attr => ({
          attributeId: attr.id,  // 保存属性ID（编码）
          name: attr.name,
          value: attr.values && attr.values.length > 0 ? attr.values[0] : ''
        }));
      });
    },
    isSubset(arr1, arr2) {
      // 将数组转换为 Set，以便进行高效的包含检查
      const set1 = new Set(arr1);
      const set2 = new Set(arr2);

      // 检查 set2 中的每个元素是否都在 set1 中
      for (let elem of set2) {
        if (!set1.has(elem)) {
          return false;
        }
      }
      return true;
    },
    // 批量添加
    batchAdd() {
      let arr = [];
      for (let val of this.attrs) {
        if (this.oneFormBatch[0][val.value]) {
          arr.push(this.oneFormBatch[0][val.value]);
        }
      }

      // 批量设置商品规格属性
      const batchFields = ['pic', 'price', 'cost', 'ot_price', 'stock', 'weight', 'volume'];
      const defaultFields = ['bar_code', 'bar_code_number'];

      for (let val of this.manyFormValidate) {
        const batch = this.oneFormBatch[0];
        // 如果存在筛选条件且满足条件,或无筛选条件时
        if (!arr.length || this.isSubset(val.attr_arr, arr)) {
          // 设置有值的批量字段
          batchFields.forEach((field) => {
            if (batch[field] !== undefined) {
              if (field === 'pic' && batch[field]) {
                this.$set(val, field, batch[field]);
              } else if (field != 'pic') {
                this.$set(val, field, batch[field]);
              }
            }
          });

          // 设置默认字段
          defaultFields.forEach((field) => {
            this.$set(val, field, batch[field]);
          });
        }
      }
    },
    changeSpecImg(arr, img) {
      // 判断是否存在规格图
      let isHas = false;
      for (let i = 1; i < this.manyFormValidate.length; i++) {
        let item = this.manyFormValidate[i];
        if (item.pic && this.isSubset(item.attr_arr, arr)) {
          isHas = true;
          break;
        }
      }
      if (isHas) {
        this.$confirm('可以同步修改下方该规格图片，确定要替换吗？', '提示', {
          confirmButtonText: '替换',
          cancelButtonText: '暂不',
          type: 'warning',
        })
          .then(() => {
            for (let val of this.manyFormValidate) {
              if (this.isSubset(val.attr_arr, arr)) {
                this.$set(val, 'pic', img);
              }
            }
          })
          .catch(() => {});
      } else {
        for (let val of this.manyFormValidate) {
          if (this.isSubset(val.attr_arr, arr)) {
            this.$set(val, 'pic', img);
          }
        }
      }
    },
    // 立即生成
    generate(type, isCopy, arr) {
      this.manyFormValidate = [];
      this.formValidate.header = [];
    },
    clearAttr() {
      this.formDynamic.attrsName = '';
      this.formDynamic.attrsVal = '';
    },

    // 删除规格
    handleRemoveRole(index) {
      this.attrs.splice(index, 1);
      this.manyFormValidate.splice(index, 1);
      if (!this.attrs.length) {
        this.formValidate.header = [];
        this.manyFormValidate = [];
      } else {
        this.generateAttr(this.attrs);
      }
    },
    // 删除表格中 对应属性
    delAttrTable(val) {
      for (let i = 0; i < this.manyFormValidate.length; i++) {
        let item = this.manyFormValidate[i];
        if (item.attr_arr && item.attr_arr.includes(val)) {
          this.manyFormValidate.splice(i, 1);
          i--;
        }
      }
    },
    // 删除属性
    handleRemove2(item, index, val) {
      // 删除 manyFormValidate中 title = item.value 的属性值
      item.splice(index, 1);
      // this.generateAttr(this.attrs);
      this.delAttrTable(val);
    },
    // 新增规格
    handleAddRole() {
      let data = {
        value: this.formDynamic.attrsName,
        add_pic: 0,
        detail: [],
      };
      this.attrs.push(data);
    },
    handleAddParams() {
      let data = {
        attributeId: null,  // 手动添加的参数暂时没有attributeId
        name: '',
        value: '',
      };
      this.formValidate.params_list.push(data);
    },
    handleSaveAsTemplate() {
      this.$prompt('', '请输入模板名称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(({ value }) => {
          let spec = this.attrs.map((item) => {
            return {
              value: item.value,
              detail: item.detail.map((e) => e.value),
            };
          });
          let formDynamic = {
            rule_name: value,
            spec: spec,
          };
          ruleAddApi(formDynamic, 0)
            .then((res) => {
              this.$message.success(res.msg);
              this.productGetRule();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
        })
        .catch(() => {});
    },
    // 新增一条属性
    addOneAttr(val, val2) {
      // 在编辑模式下，需要特殊处理规格值添加
      if (this.manyFormValidate.length > 0) {
        // 保存当前的批量设置行数据
        const batchData = { ...this.manyFormValidate[0] };

        // 重新生成属性组合
        this.generateAttr(this.attrs, val2);

        // 确保批量设置行数据不丢失，并包含所有必要的属性
        if (this.manyFormValidate.length > 0) {
          // 合并保存的数据和新的属性
          Object.keys(batchData).forEach(key => {
            if (batchData[key] !== undefined && batchData[key] !== '') {
              this.$set(this.manyFormValidate[0], key, batchData[key]);
            }
          });
        }

        // 强制更新表格
        this.$nextTick(() => {
          this.tableKey += 1;
        });
      } else {
        this.generateAttr(this.attrs, val2);
      }
    },
    handleFocus(val) {
      this.changeAttrValue = val;
    },
    handleBlur() {
      this.changeAttrValue = '';
    },
    handleSelImg(item) {
      this.$imgModal((e) => {
        // 支持多种字段名格式
        let imageUrl = '';
        if (e.att_dir) {
          imageUrl = e.att_dir;
        } else if (e.url) {
          imageUrl = e.url;
        } else if (e.src) {
          imageUrl = e.src;
        }

        item.pic = imageUrl;
        this.changeSpecImg([item.value], imageUrl);
      });
    },
    handleRemoveImg(item) {
      item.pic = '';
    },
    // 规格名称改变
    attrChangeValue(i, val) {
      if (val.trim().length && this.attrs[i].detail.length) {
        this.generateHeader(this.attrs);
        if (this.manyFormValidate.length) {
          this.manyFormValidate.map((item, i) => {
            if (i > 0) {
              if (Object.keys(item.detail).includes(this.changeAttrValue)) {
                item.detail[val] = item.detail[this.changeAttrValue];
                item[val] = item[this.changeAttrValue];
                delete item.detail[this.changeAttrValue];
                delete item[this.changeAttrValue];
              }
            }
          });
          this.changeAttrValue = val;
        }
      } else {
        this.generateAttr(this.attrs);
      }
    },
    // 规格值改变
    attrDetailChangeValue(val, i) {
      if (this.manyFormValidate.length) {
        let key = this.attrs[i].value;
        this.manyFormValidate.map((item, i) => {
          if (i > 0) {
            if (Object.keys(item.detail).includes(key) && item.detail[key] === this.changeAttrValue) {
              item.detail[key] = val;
              let index = item.attr_arr.findIndex((item) => item === this.changeAttrValue);
              item.attr_arr[index] = val;
            }
          }
        });
        this.changeAttrValue = val;
      } else {
        this.generateAttr(this.attrs, 1);
      }
    },
    // 规格图片添加开关
    addPic(e, i) {
      if (e) {
        this.attrs.map((item, ii) => {
          if (ii !== i) {
            this.$set(item, 'add_pic', 0);
          }
        });
        this.canSel = false;
      } else {
        this.canSel = true;
      }
    },
    // 规格拖拽排序后
    onMoveSpec() {
      this.generateAttr(this.attrs);
    },
    changeCurrentIndex(i) {
      this.currentIndex = i;
    },
    // 生成商品规格表头
    generateHeader(data) {
      let specificationsColumns = data.map((item) => ({
        title: item.value,
        key: item.value,
        minWidth: 140,
        fixed: 'left',
      }));
      let arr;
      if ([1, 2].includes(Number(this.formValidate.virtual_type))) {
        arr = [...specificationsColumns, ...VirtualTableHead];
        // 找到slot 等于 fictitious 将title改为规格名称

        this.formValidate.header.map((item) => {
          if (item.slot === 'fictitious') {
            item.title = this.formValidate.virtual_type == 1 ? '添加卡密/网盘' : '选择优惠券';
          }
        });
      } else if (this.formValidate.virtual_type == 3) {
        arr = [...specificationsColumns, ...VirtualTableHead2];
      } else {
        arr = [...specificationsColumns, ...GoodsTableHead];
      }
      this.$set(this.formValidate, 'header', arr);

      // 确保 oneFormBatch[0] 包含所有动态规格属性
      if (this.oneFormBatch && this.oneFormBatch[0]) {
        this.ensureBatchDataProperties(this.oneFormBatch[0], data);
      }

      this.tableKey += 1;
      this.columnsInstalM = arr;
    },
    /*
     * 生成属性
     * @param {Array} data 规格数据
     * */
    generateAttr(data, val) {
      // 判断该段Js执行时间
      console.time('generateAttr');
      this.generateHeader(data);
      const combinations = this.generateCombinations(data);

      // 保存当前的批量设置行数据（如果存在）
      let currentBatchData = null;
      if (this.manyFormValidate.length > 0) {
        currentBatchData = { ...this.manyFormValidate[0] };
      }

      let rows = combinations.map((combination) => {
        const row = {
          attr_arr: combination,
          detail: {},
          title: '',
          key: '',
          price: 0,
          pic: '',
          ot_price: 0,
          cost: 0,
          stock: 0,
          is_show: 1,
          is_default_select: 0,
          unique: '',
          weight: '',
          volume: '',
          bar_code: '',
          bar_code_number: '',
        };
        // 判断商品类型是卡密/优惠券
        let virtualType = this.formValidate.virtual_type;
        if (virtualType == 1) {
          this.$set(row, 'virtual_list', []);
          this.$set(row, 'disk_info', '');
        } else if (virtualType == 2) {
          this.$set(row, 'coupon_id', 0);
          this.$set(row, 'coupon_name', '');
        }
        for (let i = 0; i < combination.length; i++) {
          const value = combination[i];
          this.$set(row, data[i].value, value);
          this.$set(row, 'title', data[i].value);
          this.$set(row, 'key', data[i].value);
          this.$set(row.detail, data[i].value, value);
          // 如果manyFormValidate中存在该属性值，则赋值
          for (let k = 0; k < this.manyFormValidate.length; k++) {
            const manyItem = this.manyFormValidate[k];
            // 对比两个数组是否完全相等
            if (k > 0 && manyItem.attr_arr && arraysEqual(manyItem.attr_arr, combination)) {
              Object.assign(row, {
                price: manyItem.price,
                cost: manyItem.cost,
                ot_price: manyItem.ot_price,
                stock: manyItem.stock,
                pic: manyItem.pic,
                unique: manyItem.unique || '',
                weight: manyItem.weight || '',
                volume: manyItem.volume || '',
                is_show: manyItem.is_show || 1,
                is_default_select: manyItem.is_default_select || 0,
                bar_code: manyItem.bar_code || '',
                bar_code_number: manyItem.bar_code_number || '',
                is_virtual: manyItem.is_virtual,
              });

              if (virtualType == 1) {
                row.virtual_list = manyItem.virtual_list || [];
                row.disk_info = manyItem.disk_info || '';
              } else if (virtualType == 2 && manyItem.coupon_id) {
                row.coupon_id = manyItem.coupon_id;
                row.coupon_name = manyItem.coupon_name;
              }
            } else if (k > 0 && manyItem.attr_arr && manyItem.attr_arr.length && data[i].add_pic && combination.includes(val)) {
              // data[i].detail中的value是规格值 存在与 manyItem.attr_arr 中的某一项
              data[i].detail.map((e, ii) => {
                combination.includes(e.value) && this.$set(row, 'pic', e.pic);
              });
            }
          }
        }
        return row;
      });

      this.$nextTick(() => {
        // 使用保存的批量设置行数据，如果没有则使用默认的 oneFormBatch
        const batchRow = currentBatchData || this.oneFormBatch[0];

        // 确保批量设置行包含所有动态规格属性
        this.ensureBatchDataProperties(batchRow, data);

        // rows数组第一项 新增默认数据 oneFormBatch
        this.manyFormValidate = [batchRow, ...rows];

        // 强制更新表格组件
        this.tableKey += 1;
      });
      console.timeEnd('generateAttr');
    },
    // 切换默认选中规格
    changeDefaultSelect(e, index) {
      // 一个开启 其他关闭
      this.manyFormValidate.map((item, i) => {
        if (i !== index) {
          item.is_default_select = 0;
        }
      });
      if (e) this.manyFormValidate[index].is_show = 1;
    },
    // 改变是否显示
    changeDefaultShow(index) {
      // 如果默认选中开启 则不可隐藏
      if (this.manyFormValidate[index].is_default_select === 1) {
        this.manyFormValidate[index].is_show = 1;
        this.$message.error('默认规格不可隐藏');
      }
    },
    // 生成规格组合
    generateCombinations(arr, prefix = []) {
      if (arr.length === 0) {
        return [prefix];
      }
      const [first, ...rest] = arr;
      return first.detail.flatMap((detail) => this.generateCombinations(rest, [...prefix, detail.value]));
    },
    // 添加属性
    createAttr(num, idx) {
      if (num) {
        // 判断是否存在同样熟悉
        var isExist = this.attrs[idx].detail.some((item) => item.value === num);
        if (isExist) {
          this.$message.error('规格值已存在');
          return;
        }
        this.attrs[idx].detail.push({ value: num, pic: '' });

        // 无论是否有现有数据，都需要重新生成属性组合
        if (this.manyFormValidate.length > 0) {
          // 如果已有数据，调用 addOneAttr 来处理增量更新
          this.addOneAttr(this.attrs[idx].value, num);
        } else {
          // 如果没有数据，直接生成
          this.generateAttr(this.attrs);
        }

        this.$refs.specStock.$refs['popoverRef_' + idx][0].doClose(); //关闭的
        this.clearAttr();
        setTimeout(() => {
          if (this.$refs.specStock.$refs['popoverRef_' + idx]) {
            //重点是以下两句
            this.$refs.specStock.$refs['popoverRef_' + idx][0].doShow(); //打开的
            //重点是以上两句
          }
        }, 20);
      } else {
        this.$refs.specStock.$refs['popoverRef_' + idx][0].doClose(); //关闭的
      }
    },
    handleShowPop(index) {
      this.$refs.specStock.$refs['inputRef_' + index][0].focus();
    },
    // 商品分类；
    goodsCategory() {
      cascaderListApi(0)
        .then((res) => {
          this.treeSelect = res.data;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 改变规格
    changeSpec(val) {
      this.formValidate.is_sub = [];

      // 当切换到多规格时，确保数据结构正确初始化
      if (val === 1) {
        // 如果 attrs 为空，初始化一个空的规格
        if (!this.attrs || this.attrs.length === 0) {
          this.attrs = [];
        }

        // 确保 manyFormValidate 有正确的批量设置行
        if (!this.manyFormValidate || this.manyFormValidate.length === 0) {
          // 确保 oneFormBatch[0] 包含所有必要的属性
          if (this.attrs.length > 0) {
            this.attrs.forEach((item) => {
              if (!this.oneFormBatch[0].hasOwnProperty(item.value)) {
                this.$set(this.oneFormBatch[0], item.value, '');
              }
            });
          }
          this.manyFormValidate = [...this.oneFormBatch];
        }

        // 强制更新表格
        this.$nextTick(() => {
          this.tableKey += 1;
        });
      } else {
        // 切换到单规格时，清空多规格相关数据
        this.manyFormValidate = [];
        this.attrs = [];
      }

      let id = this.$route.params.id;
      if (id) {
        checkActivityApi(id)
          .then((res) => {})
          .catch((res) => {
            this.formValidate.spec_type = this.spec_type;
            this.$message.error(res.msg);
          });
      }
    },
    // 详情
    getInfo() {
      this.spinShow = true;
      productInfoApi(this.$route.params.id)
        .then(async (res) => {
          this.spinShow = false;
          if (res.success) {
            const productInfo = res.data.productInfo;

            // Set basic product information
            this.formValidate.id = productInfo.id;
            this.formValidate.store_name = productInfo.name;
            this.formValidate.store_info = productInfo.goodsBrief;
            this.formValidate.keyword = productInfo.keywords;
            this.formValidate.is_show = productInfo.isOnSale;
            this.formValidate.is_hot = productInfo.isHot;
            this.formValidate.is_new = productInfo.isNewly;
            this.formValidate.sort = productInfo.sortOrder;
            this.formValidate.ficti = productInfo.sellVolume;
            this.formValidate.unit_name = productInfo.goodsUnit;
            this.formValidate.unit_price = productInfo.unitPrice || 0;
            this.formValidate.image = productInfo.listPicUrl;
            this.formValidate.temp_id = productInfo.freightTemplateId;

            // Set display systems
            if (productInfo.displaySystems && Array.isArray(productInfo.displaySystems)) {
              this.formValidate.display_systems = productInfo.displaySystems;
            } else {
              this.formValidate.display_systems = [];
            }

            // Set detail tag
            this.formValidate.detail_tag = productInfo.detailTag || '';

            // Set content
            this.content = productInfo.goodsDesc;

            // Set category
            if (productInfo.categoryId) {
              this.getCategoryPath(productInfo.categoryId);
            }

            // Set slider images
            if (res.data.goodsGallery && res.data.goodsGallery.length) {
              this.formValidate.slider_image = res.data.goodsGallery.map(item => item.imgUrl);
            }

            // Set specifications and products
            if (res.data.products && res.data.products.length) {
              const products = res.data.products;

              // Check if this is truly a multi-spec product by looking at specifications data
              const hasSpecifications = res.data.specifications && res.data.specifications.length > 0;

              if (products.length === 1 || !hasSpecifications) {
                // Single specification (either only 1 product OR no specification data)
                this.formValidate.spec_type = 0;
                this.oneFormValidate = [{
                  price: products[0].retailPrice,
                  cost: products[0].costPrice || 0,
                  stock: products[0].goodsNumber,
                  bar_code: products[0].goodsSn || '',
                  weight: products[0].goodsWeight || 0,
                  volume: products[0].goodsVolume || 0,
                  pic: products[0].picUrl || this.formValidate.image
                }];
                this.attrs = [];
                this.manyFormValidate = [];
              } else {
                // Multiple specifications (multiple products AND has specification data)
                this.formValidate.spec_type = 1;

                // Load specifications for attrs
                this.attrs = res.data.specifications;

                // Generate header for specification table
                this.generateHeader(this.attrs);

                // Load products with specifications and build manyFormValidate
                // In edit mode, don't include oneFormBatch as first row should show actual data
                // In create mode, include oneFormBatch for batch setting functionality
                const isEditMode = !!this.$route.params.id && this.$route.params.id !== '0';
                this.manyFormValidate = isEditMode ? [] : [...this.oneFormBatch];

                products.forEach(product => {
                  const productItem = {
                    price: product.retailPrice,
                    cost: product.costPrice || 0,
                    stock: product.goodsNumber,
                    bar_code: product.goodsSn || '',
                    weight: product.goodsWeight || 0,
                    volume: product.goodsVolume || 0,
                    pic: product.picUrl || this.formValidate.image,
                    detail: {},
                    attr_arr: [],
                    is_default_select: 0,
                    is_show: 1
                  };

                  // Build attr_arr and detail from specifications
                  if (product.specifications && typeof product.specifications === 'object') {
                    productItem.attr_arr = Object.values(product.specifications);

                    // Set detail object for table display - this is crucial for showing spec values
                    productItem.detail = { ...product.specifications };

                    // Also set individual specification fields for table display
                    Object.keys(product.specifications).forEach(specName => {
                      productItem[specName] = product.specifications[specName];
                    });
                  }

                  this.manyFormValidate.push(productItem);
                });

                console.log('manyFormValidate after processing:', this.manyFormValidate);
                console.log('First item detail:', this.manyFormValidate[0]?.detail);

                this.createBnt = true;

                // 尝试匹配当前规格与规格模板
                this.detectCurrentTemplate();
              }
            }

            // Load attributes
            if (res.data.attributes && res.data.attributes.length) {
              this.formValidate.params_list = res.data.attributes.map(item => {
                return {
                  attributeId: item.attributeId,  // 保存属性ID（编码）
                  name: item.attributeName,
                  value: item.attributeValue
                };
              });

              // 如果有参数数据，尝试设置参数类型
              // 通过第一个参数的attributeId查找对应的分类
              if (this.formValidate.params_list.length > 0 && this.formValidate.params_list[0].attributeId) {
                this.setParamsTypeFromAttributes();
              }
            }
          } else {
            this.$message.error(res.message || '获取商品信息失败');
          }
        })
        .catch((err) => {
          this.spinShow = false;
          this.$message.error(err.message || '获取商品信息失败');
        });
    },
    // 检测当前使用的规格模板
    detectCurrentTemplate() {
      if (!this.attrs || this.attrs.length === 0 || !this.ruleList || this.ruleList.length === 0) {
        return;
      }

      // 遍历规格模板列表，找到匹配的模板
      for (const template of this.ruleList) {
        if (this.isTemplateMatch(template.rule_value, this.attrs)) {
          this.currentTemplateName = template.rule_name;
          this.formValidate.selectRule = template.rule_name;
          break;
        }
      }
    },

    // 检查规格模板是否匹配当前规格
    isTemplateMatch(templateSpecs, currentSpecs) {
      if (!templateSpecs || !currentSpecs || templateSpecs.length !== currentSpecs.length) {
        return false;
      }

      // 创建规格名称和值的映射
      const templateMap = {};
      templateSpecs.forEach(spec => {
        templateMap[spec.value] = spec.detail.map(d => d.value).sort();
      });

      const currentMap = {};
      currentSpecs.forEach(spec => {
        currentMap[spec.value] = spec.detail.map(d => d.value).sort();
      });

      // 比较规格名称和值
      const templateKeys = Object.keys(templateMap).sort();
      const currentKeys = Object.keys(currentMap).sort();

      if (templateKeys.length !== currentKeys.length) {
        return false;
      }

      for (let i = 0; i < templateKeys.length; i++) {
        if (templateKeys[i] !== currentKeys[i]) {
          return false;
        }

        const templateValues = templateMap[templateKeys[i]];
        const currentValues = currentMap[currentKeys[i]];

        if (templateValues.length !== currentValues.length) {
          return false;
        }

        for (let j = 0; j < templateValues.length; j++) {
          if (templateValues[j] !== currentValues[j]) {
            return false;
          }
        }
      }

      return true;
    },

    // Get category path for editing
    getCategoryPath(categoryId) {
      // Get category path by category ID
      cascaderListApi(0)
        .then((res) => {
          if (res.success) {
            const categories = res.data;
            const findPath = (categories, targetId, path = []) => {
              for (const category of categories) {
                const newPath = [...path, category.value];
                if (category.value === targetId) {
                  return newPath;
                }
                if (category.children && category.children.length) {
                  const foundPath = findPath(category.children, targetId, newPath);
                  if (foundPath) return foundPath;
                }
              }
              return null;
            };

            const path = findPath(categories, categoryId);
            if (path) {
              this.formValidate.cate_id = path;
            }
          }
        });
    },
    handleRemove(i) {
      const removedImage = this.formValidate.slider_image[i];

      // 如果删除的是第一张轮播图，且主图与第一张轮播图相同，提示用户
      if (i === 0 && this.formValidate.image === removedImage && this.formValidate.slider_image.length > 1) {
        this.$confirm('删除的是第一张轮播图，且当前主图与此图片相同，是否将第二张轮播图设为主图？', '提示', {
          confirmButtonText: '是，更新主图',
          cancelButtonText: '否，保持主图',
          type: 'warning',
        }).then(() => {
          // 用户选择更新主图
          this.formValidate.image = this.formValidate.slider_image[1];
          this.removeSliderImage(i);
        }).catch(() => {
          // 用户选择保持主图不变
          this.removeSliderImage(i);
        });
      } else {
        // 直接删除
        this.removeSliderImage(i);
      }
    },

    // 删除轮播图的具体逻辑
    removeSliderImage(i) {
      this.images.splice(i, 1);
      this.formValidate.slider_image.splice(i, 1);

      // 更新单规格商品的图片
      if (this.oneFormValidate && this.oneFormValidate.length > 0) {
        this.oneFormValidate[0].pic = this.formValidate.slider_image[0] || '';
      }
    },
    // 关闭图片上传模态框
    changeCancel(msg) {
      this.modalPic = false;
    },
    // 点击商品图
    modalPicTap(tit, index) {
      this.modalPic = true;
      this.picTit = tit;
      this.modalPicIndex = index;
      // 主图选择使用单选模式，轮播图使用多选模式
      if (tit === 'main_image') {
        this.isChoice = '单选';
      } else if (tit === 'slider_image' || tit === 'duo') {
        this.isChoice = '多选';
      } else {
        this.isChoice = '单选'; // 默认单选
      }
      console.log('modalPicTap - 选择类型:', tit, '选择模式:', this.isChoice);
    },
    // 获取单张图片信息
    getPic(pc) {
      console.log('getPic 接收到的图片数据:', pc);
      console.log('当前选择类型:', this.picTit);

      // 获取图片URL，支持多种字段名格式
      let imageUrl = '';
      if (pc.att_dir) {
        imageUrl = pc.att_dir;
      } else if (pc.url) {
        imageUrl = pc.url;
      } else if (pc.src) {
        imageUrl = pc.src;
      }

      // 根据选择类型设置对应的图片
      if (this.picTit === 'main_image') {
        // 设置主图
        this.formValidate.image = imageUrl;
        console.log('设置主图:', this.formValidate.image);
      } else {
        // 其他情况的处理（如规格图片等）
        console.log('处理其他类型图片:', this.picTit);
        // 这里可以根据具体的 picTit 类型进行不同的处理
      }

      this.modalPic = false;
      this.tableKey++; // Force refresh
    },
    deleteRow(index) {
      this.formValidate.params_list.splice(index, 1);
    },
    // 获取多张图信息
    getPicD(pc) {
      console.log('getPicD 接收到的图片数据:', pc);
      this.images = pc;

      // 清空现有图片数组
      this.formValidate.slider_image = [];

      // 添加轮播图（最多10张）
      if (this.images.length > 0) {
        this.images.slice(0, 10).forEach((item) => {
          let itemUrl = '';
          if (item.att_dir) {
            itemUrl = item.att_dir;
          } else if (item.url) {
            itemUrl = item.url;
          } else if (item.src) {
            itemUrl = item.src;
          }

          if (itemUrl) {
            this.formValidate.slider_image.push(itemUrl);
          }
        });
        console.log('设置轮播图:', this.formValidate.slider_image);

        // 更新规格图片
        if (this.oneFormValidate && this.oneFormValidate.length > 0) {
          this.oneFormValidate[0].pic = this.formValidate.slider_image[0];
        }
      }

      this.modalPic = false;

      // Force refresh of images by triggering a re-render
      this.$nextTick(() => {
        this.tableKey += 1;
      });
    },
    // 提交
    handleSubmit(name) {
      console.log('提交验证 - 主图:', this.formValidate.image);
      console.log('提交验证 - 轮播图:', this.formValidate.slider_image);
      console.log('提交验证 - 分类:', this.formValidate.cate_id);

      this.$refs[name].validate((valid) => {
        if (valid) {
          if (!this.formValidate.cate_id || !this.formValidate.cate_id.length) {
            return this.$message.error('请选择商品分类');
          }
          if (!this.formValidate.image) {
            return this.$message.error('请上传商品主图');
          }
          if (!this.formValidate.slider_image.length) {
            return this.$message.error('请上传商品轮播图');
          }

          // Prepare data for submission
          let data = {
            id: this.formValidate.id || 0,
            storeName: this.formValidate.store_name,
            storeInfo: this.formValidate.store_info,
            keyword: this.formValidate.keyword,
            categoryIds: this.formValidate.cate_id,
            unitName: this.formValidate.unit_name,
            unitPrice: this.formValidate.unit_price,
            image: this.formValidate.image,
            sliderImages: this.formValidate.slider_image,
            description: this.content,
            specType: this.formValidate.spec_type,
            isShow: this.formValidate.is_show ? 1 : 0,
            isHot: this.formValidate.is_hot ? 1 : 0,
            isNew: this.formValidate.is_new ? 1 : 0,
            sort: this.formValidate.sort,
            virtualSales: this.formValidate.ficti,
            tempId: this.formValidate.temp_id,
            displaySystems: this.formValidate.display_systems || [],
            detailTag: this.formValidate.detail_tag || '',
            attrs: [],
            items: this.formValidate.spec_type === 1 ? this.attrs : []
          };

          console.log('准备提交的数据:', data);

          // Handle specifications based on spec_type
          if (this.formValidate.spec_type === 0) {
            // Single specification
            data.attrs = [{
              price: this.oneFormValidate[0].price,
              cost: this.oneFormValidate[0].cost,
              stock: this.oneFormValidate[0].stock,
              barCode: this.oneFormValidate[0].bar_code,
              weight: this.oneFormValidate[0].weight,
              volume: this.oneFormValidate[0].volume,
              pic: this.oneFormValidate[0].pic
            }];
          } else {
            // Multiple specifications
            data.attrs = this.manyFormValidate.map(item => {
              return {
                price: item.price,
                cost: item.cost,
                stock: item.stock,
                barCode: item.bar_code,
                weight: item.weight,
                volume: item.volume,
                pic: item.pic,
                detail: item.detail
              };
            });
          }

          // Add product parameters
          if (this.formValidate.params_list && this.formValidate.params_list.length) {
            data.paramsList = this.formValidate.params_list.map(item => {
              return {
                attributeId: item.attributeId,  // 包含属性ID（编码）
                name: item.name,
                value: item.value
              };
            });
          }

          this.spinShow = true;
          productAddApi(data)
            .then((res) => {
              this.spinShow = false;
              if (res.success) {
                this.$message.success('保存成功');
                setTimeout(() => {
                  this.$router.push({
                    path: `${this.$routeProStr}/product/product_list`,
                  });
                }, 500);
              } else {
                this.$message.error(res.message);
              }
            })
            .catch((err) => {
              this.spinShow = false;
              this.$message.error(err.message || '保存失败');
            });
        }
      });
    },
    changeTemplate(msg) {
      this.template = msg;
    },
    // 表单验证
    validate(prop, status, error) {
      if (status === false) {
        this.$message.warning(error);
      }
    },
    // 移动
    handleDragStart(e, item) {
      this.dragging = item;
    },
    handleDragEnd(e, item) {
      this.dragging = null;
    },
    handleDragOver(e) {
      e.dataTransfer.dropEffect = 'move';
    },
    handleDragEnter(e, item) {
      e.dataTransfer.effectAllowed = 'move';
      if (item === this.dragging) {
        return;
      }
      const newItems = [...this.formValidate.slider_image];
      const src = newItems.indexOf(this.dragging);
      const dst = newItems.indexOf(item);
      newItems.splice(dst, 0, ...newItems.splice(src, 1));
      this.formValidate.slider_image = newItems;
    },
    //对象数组去重；
    unique(arr) {
      const res = new Map();
      return arr.filter((arr) => !res.has(arr.product_id) && res.set(arr.product_id, 1));
    },
    // 商品id
    getProductId(data) {
      this.goods_modals = false;
      this.formValidate.recommend_list = this.unique(this.formValidate.recommend_list.concat(data));
    },
    // 选择推荐商品
    changeGoods() {
      this.goods_modals = true;
      this.$refs.goodslist.getList();
      this.$refs.goodslist.goodsCategory();
    },
    // 选择用户标签
    activeData(dataLabel) {
      this.labelShow = false;
      this.dataLabel = dataLabel;
    },
    // 选择商品标签
    activeLabel(data) {
      this.tagShow = false;
      this.formValidate.label_list = Array.from(new Set(data));
    },
    // 删除用户标签
    closeLabel(id) {
      const index = this.dataLabel.findIndex(item => item.id === id);
      if (index !== -1) {
        this.dataLabel.splice(index, 1);
      }
    },
    // 标签弹窗关闭
    labelClose() {
      this.labelShow = false;
      this.tagShow = false;
    },
    // 打开选择用户标签
    openLabel(row) {
      this.labelShow = true;
    },
    handleRemoveRecommend(i) {
      this.formValidate.recommend_list.splice(i, 1);
    },
    // 打开的营销活动标签
    watchActivity() {
      // Since we're removing marketing activities, just set a default value
      this.formValidate.activity = ['默认'];
    },
  },
};
</script>
<style lang="scss" scoped>
@import './productAdd.scss';
</style>
